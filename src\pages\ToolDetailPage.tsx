import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, CheckCircle, Users, Star, ArrowRight, AlertTriangle, Sparkles, Zap, Shield, Microscope, Calculator, Calendar, Camera, BarChart3 } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const ToolDetailPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();

  // All useState hooks must be at the top level
  const [quizStarted, setQuizStarted] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [quizAnswers, setQuizAnswers] = useState({
    skinType: '',
    concerns: [] as string[],
    sensitivity: '',
    budget: '',
  });
  const [routineResult, setRoutineResult] = useState<null | {
    morning: string[];
    evening: string[];
    weekly: string[];
  }>(null);
  const [ingredientQuery, setIngredientQuery] = useState('');
  const [skinType, setSkinType] = useState('');
  const [analysisResult, setAnalysisResult] = useState<null | Record<string, unknown>>(null);
  const [selectedActive, setSelectedActive] = useState('');
  const [experienceLevel, setExperienceLevel] = useState('');
  const [concentrationResult, setConcentrationResult] = useState<null | Record<string, unknown>>(null);
  const [trackerStep, setTrackerStep] = useState('setup');
  const [routineData, setRoutineData] = useState<Record<string, unknown> | null>(null);
  const [products, setProducts] = useState(['']);
  const [phResults, setPHResults] = useState<null | Record<string, unknown>>(null);

  // Mock data for the tools
  const tools = {
    'ingredient-compatibility-checker': {
      title: 'Ingredient Compatibility Checker',
      description: 'Check if your skincare ingredients work well together or if there are potential conflicts in your routine.',
      longDescription: 'Our advanced compatibility checker analyzes ingredient interactions based on scientific research, pH requirements, and potential for irritation. Avoid conflicts in your routine and maximize the effectiveness of your products.',
      icon: CheckCircle,
      color: 'bg-green-500',
      features: [
        'Real-time compatibility analysis',
        'Detailed explanations of interactions',
        'Alternative suggestions for incompatible ingredients',
        'Save your combinations for future reference',
        'Backed by scientific research and dermatologist input',
        'Regular updates with new ingredient data',
      ],
      howToUse: [
        'Enter the ingredients or products you want to check',
        'Review the compatibility results and recommendations',
        'Adjust your routine based on the suggestions',
        'Save your compatible combinations',
      ],
      users: '25K+',
      rating: 4.8,
      reviews: [
        { name: 'User', comment: 'This tool saved my skin! I had no idea I was using conflicting ingredients that were canceling each other out.', rating: 5 },
        { name: 'User', comment: 'So helpful for building my routine. I love the detailed explanations of why certain ingredients don\'t work together.', rating: 5 },
        { name: 'User', comment: 'Great tool, but I wish it had more niche ingredients in its database.', rating: 4 },
      ],
    },
    'routine-builder-quiz': {
      title: 'Personalized Routine Builder',
      description: 'Take our comprehensive quiz to get a customized skincare routine based on your skin type, concerns, and lifestyle.',
      longDescription: 'Our routine builder uses advanced algorithms to create a personalized skincare regimen tailored to your unique skin profile. By analyzing your skin type, concerns, environment, and lifestyle factors, we recommend the ideal routine structure and ingredient types for your specific needs.',
      icon: Users,
      color: 'bg-blue-500',
      features: [
        'Personalized recommendations based on your skin profile',
        'Step-by-step routine structure',
        'Ingredient recommendations for each step',
        'Budget-friendly options available',
        'Seasonal routine adjustments',
        'Printable routine guide',
      ],
      howToUse: [
        'Complete the comprehensive skin assessment quiz',
        'Receive your personalized routine recommendations',
        'Follow the suggested morning and evening routines',
        'Update your profile as your skin changes',
      ],
      users: '18K+',
      rating: 4.7,
      reviews: [
        { name: 'User', comment: 'Finally a routine that actually works for my combination skin! The seasonal adjustments were especially helpful.', rating: 5 },
        { name: 'User', comment: 'I was overwhelmed by all the skincare options out there, but this quiz made it so simple to know what I actually need.', rating: 5 },
        { name: 'User', comment: 'Good recommendations but I wish there were more specific product suggestions.', rating: 4 },
      ],
    },
    'ingredient-analyzer': {
      title: 'Skincare Ingredient Analyzer',
      description: 'Analyze any skincare ingredient for safety, effectiveness, and compatibility with your skin type.',
      longDescription: 'Our comprehensive ingredient analyzer uses scientific research and dermatological data to provide detailed analysis of skincare ingredients. Get safety ratings, effectiveness scores, and personalized compatibility assessments.',
      icon: Microscope,
      color: 'bg-purple-500',
      features: [
        'Comprehensive ingredient safety analysis',
        'Evidence-based effectiveness ratings',
        'Skin type compatibility assessment',
        'Scientific research references',
        'Potential side effects and warnings',
        'Concentration recommendations',
        'Alternative ingredient suggestions',
        'Pregnancy and breastfeeding safety',
      ],
      howToUse: [
        'Enter the ingredient name or INCI name',
        'Select your skin type and concerns',
        'Review the comprehensive analysis report',
        'Get personalized recommendations and warnings',
      ],
      users: '12K+',
      rating: 4.6,
      reviews: [
        { name: 'User', comment: 'This tool helped me understand why certain ingredients were breaking me out. The safety analysis is incredibly detailed!', rating: 5 },
        { name: 'User', comment: 'Love how it explains the science behind each ingredient. Perfect for ingredient nerds like me!', rating: 5 },
        { name: 'User', comment: 'Great tool but I wish it had more obscure ingredients in the database.', rating: 4 },
      ],
    },
    'concentration-calculator': {
      title: 'Active Concentration Calculator',
      description: 'Calculate optimal concentrations for skincare actives and avoid over-dosing your skin.',
      longDescription: 'Prevent skin irritation and maximize effectiveness with our active concentration calculator. Get personalized dosage recommendations based on your skin tolerance and experience level.',
      icon: Calculator,
      color: 'bg-blue-500',
      features: [
        'Optimal concentration calculations',
        'Progressive increase schedules',
        'Safety threshold warnings',
        'Skin tolerance assessments',
        'Product mixing calculations',
        'Frequency recommendations',
        'Interaction warnings',
        'Beginner-friendly guidance',
      ],
      howToUse: [
        'Select the active ingredient you want to use',
        'Input your current skin tolerance level',
        'Get your personalized concentration recommendation',
        'Follow the progressive increase schedule',
      ],
      users: '8K+',
      rating: 4.5,
      reviews: [
        { name: 'User', comment: 'Finally! No more guessing with actives. This calculator saved my skin from retinol overuse.', rating: 5 },
        { name: 'User', comment: 'The progressive increase schedule is genius. Helped me build up to higher concentrations safely.', rating: 5 },
        { name: 'User', comment: 'Very helpful but could use more exotic actives in the database.', rating: 4 },
      ],
    },
    'routine-tracker': {
      title: 'Skincare Routine Tracker',
      description: 'Track your daily skincare routine, monitor progress, and get personalized insights.',
      longDescription: 'Transform your skincare journey with our comprehensive routine tracker. Log daily routines, track skin changes, upload progress photos, and receive AI-powered insights to optimize your regimen.',
      icon: Calendar,
      color: 'bg-indigo-500',
      features: [
        'Daily routine logging and reminders',
        'Progress photo tracking with timeline',
        'Skin condition monitoring',
        'Product effectiveness analysis',
        'Personalized insights and recommendations',
        'Routine optimization suggestions',
        'Export data for dermatologist visits',
        'Social sharing and community features',
      ],
      howToUse: [
        'Set up your current skincare routine',
        'Log daily usage and skin observations',
        'Upload progress photos regularly',
        'Review insights and adjust routine as needed',
      ],
      users: '15K+',
      rating: 4.7,
      reviews: [
        { name: 'User', comment: 'This tracker helped me identify which products were actually working. The progress photos feature is amazing!', rating: 5 },
        { name: 'User', comment: 'Love the reminders and the ability to see patterns in my skin. Great for staying consistent!', rating: 5 },
        { name: 'User', comment: 'Good tracker but the photo upload could be more user-friendly.', rating: 4 },
      ],
    },
    'ph-compatibility-tool': {
      title: 'pH Compatibility Tool',
      description: 'Ensure your products work together by checking pH compatibility and optimal layering order.',
      longDescription: 'Maximize product effectiveness and prevent conflicts with our pH compatibility tool. Get optimal layering sequences, wait times, and pH-based recommendations for your skincare routine.',
      icon: TestTube,
      color: 'bg-pink-500',
      features: [
        'pH level compatibility checking',
        'Optimal layering order recommendations',
        'Wait time calculations between products',
        'Efficacy optimization tips',
        'Product interaction warnings',
        'pH range explanations',
        'Alternative product suggestions',
        'Professional formulation insights',
      ],
      howToUse: [
        'Enter your skincare products or ingredients',
        'Review the pH compatibility analysis',
        'Follow the recommended layering order',
        'Apply suggested wait times between products',
      ],
      users: '6K+',
      rating: 4.3,
      reviews: [
        { name: 'User', comment: 'Never knew pH mattered so much! This tool completely changed how I layer my products.', rating: 5 },
        { name: 'User', comment: 'Great for understanding why some products don\'t work well together. Very educational!', rating: 4 },
        { name: 'User', comment: 'Useful tool but could be more beginner-friendly with the explanations.', rating: 4 },
      ],
    },
  };
  
  // Get the tool data based on the slug
  const tool = slug ? tools[slug as keyof typeof tools] : null;

  if (!tool) {
    return (
      <div className="pt-16 min-h-screen bg-brand-off-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-brand-charcoal mb-4">Tool Not Found</h1>
          <Link to="/tools" className="btn-primary">
            Browse All Tools
          </Link>
        </div>
      </div>
    );
  }

  // SEO Data
  const seoTitle = `${tool.title} - Free Skincare Tool | Skincare Compass`;
  const seoDescription = tool.longDescription;
  const canonicalUrl = `https://www.skincarecompass.com/tools/${slug}/`;



  // Ingredient Analyzer Interface
  const renderIngredientAnalyzer = () => {
    const handleAnalyze = () => {
      // Mock analysis result
      setAnalysisResult({
        ingredient: ingredientQuery,
        safetyRating: 4.2,
        effectivenessRating: 4.5,
        compatibility: skinType === 'sensitive' ? 'Caution' : 'Good',
        benefits: ['Hydration', 'Anti-aging', 'Barrier repair'],
        sideEffects: ['Rare allergic reactions', 'May cause initial purging'],
        recommendations: ['Start with lower concentration', 'Use sunscreen daily'],
        researchLevel: 'High',
        pregnancySafe: true,
      });
    };

    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
            <Microscope className="w-8 h-8 text-purple-600" />
          </div>
          <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
            Analyze Any Skincare Ingredient
          </h3>
          <p className="text-gray-600">
            Get comprehensive safety and effectiveness analysis for any skincare ingredient
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="ingredient" className="block text-sm font-medium text-gray-700 mb-2">
                Ingredient Name
              </label>
              <input
                type="text"
                id="ingredient"
                value={ingredientQuery}
                onChange={(e) => setIngredientQuery(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                placeholder="e.g., Hyaluronic Acid, Retinol, Niacinamide"
              />
            </div>
            <div>
              <label htmlFor="skintype" className="block text-sm font-medium text-gray-700 mb-2">
                Your Skin Type
              </label>
              <select
                id="skintype"
                value={skinType}
                onChange={(e) => setSkinType(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                <option value="">Select skin type</option>
                <option value="normal">Normal</option>
                <option value="dry">Dry</option>
                <option value="oily">Oily</option>
                <option value="combination">Combination</option>
                <option value="sensitive">Sensitive</option>
              </select>
            </div>
          </div>

          <button
            onClick={handleAnalyze}
            disabled={!ingredientQuery || !skinType}
            className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Analyze Ingredient
          </button>

          {analysisResult && (
            <div className="mt-8 space-y-6">
              <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-6">
                <h4 className="font-bold text-xl text-brand-charcoal mb-4">
                  Analysis Results for {analysisResult.ingredient}
                </h4>

                <div className="grid md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{analysisResult.safetyRating}/5</div>
                    <div className="text-sm text-gray-600">Safety Rating</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{analysisResult.effectivenessRating}/5</div>
                    <div className="text-sm text-gray-600">Effectiveness</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${analysisResult.compatibility === 'Good' ? 'text-green-600' : 'text-yellow-600'}`}>
                      {analysisResult.compatibility}
                    </div>
                    <div className="text-sm text-gray-600">Skin Compatibility</div>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-semibold text-brand-charcoal mb-2">Key Benefits</h5>
                    <ul className="space-y-1">
                      {analysisResult.benefits.map((benefit: string, index: number) => (
                        <li key={index} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-semibold text-brand-charcoal mb-2">Potential Side Effects</h5>
                    <ul className="space-y-1">
                      {analysisResult.sideEffects.map((effect: string, index: number) => (
                        <li key={index} className="flex items-center text-sm text-gray-600">
                          <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2" />
                          {effect}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-white rounded-lg">
                  <h5 className="font-semibold text-brand-charcoal mb-2">Recommendations</h5>
                  <ul className="space-y-1">
                    {analysisResult.recommendations.map((rec: string, index: number) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <Sparkles className="w-4 h-4 text-brand-teal mr-2" />
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Concentration Calculator Interface
  const renderConcentrationCalculator = () => {
    const actives = [
      { name: 'Retinol', beginnerMax: 0.25, intermediateMax: 0.5, advancedMax: 1.0 },
      { name: 'Vitamin C (L-Ascorbic Acid)', beginnerMax: 10, intermediateMax: 15, advancedMax: 20 },
      { name: 'Niacinamide', beginnerMax: 5, intermediateMax: 10, advancedMax: 20 },
      { name: 'Glycolic Acid', beginnerMax: 5, intermediateMax: 10, advancedMax: 15 },
      { name: 'Salicylic Acid', beginnerMax: 0.5, intermediateMax: 1, advancedMax: 2 },
    ];

    const handleCalculate = () => {
      const active = actives.find(a => a.name === selectedActive);
      if (!active) return;

      let maxConcentration = 0;
      let startingConcentration = 0;
      let frequency = '';

      switch (experienceLevel) {
        case 'beginner':
          maxConcentration = active.beginnerMax;
          startingConcentration = maxConcentration * 0.5;
          frequency = '2-3 times per week';
          break;
        case 'intermediate':
          maxConcentration = active.intermediateMax;
          startingConcentration = active.beginnerMax;
          frequency = 'Every other day';
          break;
        case 'advanced':
          maxConcentration = active.advancedMax;
          startingConcentration = active.intermediateMax;
          frequency = 'Daily (if tolerated)';
          break;
      }

      setConcentrationResult({
        active: selectedActive,
        startingConcentration,
        maxConcentration,
        frequency,
        schedule: [
          { week: '1-2', concentration: startingConcentration, frequency: '2x per week' },
          { week: '3-4', concentration: startingConcentration, frequency: '3x per week' },
          { week: '5-8', concentration: maxConcentration * 0.75, frequency: 'Every other day' },
          { week: '9+', concentration: maxConcentration, frequency: frequency },
        ]
      });
    };

    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
            <Calculator className="w-8 h-8 text-blue-600" />
          </div>
          <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
            Calculate Safe Active Concentrations
          </h3>
          <p className="text-gray-600">
            Get personalized concentration recommendations to avoid irritation and maximize results
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="active" className="block text-sm font-medium text-gray-700 mb-2">
                Active Ingredient
              </label>
              <select
                id="active"
                value={selectedActive}
                onChange={(e) => setSelectedActive(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                <option value="">Select an active</option>
                {actives.map(active => (
                  <option key={active.name} value={active.name}>{active.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="experience" className="block text-sm font-medium text-gray-700 mb-2">
                Experience Level
              </label>
              <select
                id="experience"
                value={experienceLevel}
                onChange={(e) => setExperienceLevel(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                <option value="">Select experience</option>
                <option value="beginner">Beginner (New to actives)</option>
                <option value="intermediate">Intermediate (Some experience)</option>
                <option value="advanced">Advanced (Experienced user)</option>
              </select>
            </div>
          </div>

          <button
            onClick={handleCalculate}
            disabled={!selectedActive || !experienceLevel}
            className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Calculate Concentration
          </button>

          {concentrationResult && (
            <div className="mt-8 space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6">
                <h4 className="font-bold text-xl text-brand-charcoal mb-4">
                  Concentration Plan for {concentrationResult.active}
                </h4>

                <div className="grid md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{concentrationResult.startingConcentration}%</div>
                    <div className="text-sm text-gray-600">Starting Dose</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{concentrationResult.maxConcentration}%</div>
                    <div className="text-sm text-gray-600">Target Dose</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">{concentrationResult.frequency}</div>
                    <div className="text-sm text-gray-600">Final Frequency</div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4">
                  <h5 className="font-semibold text-brand-charcoal mb-3">Progressive Increase Schedule</h5>
                  <div className="space-y-3">
                    {concentrationResult.schedule.map((phase: { week: string; frequency: string; concentration: string }, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium text-brand-charcoal">Week {phase.week}</div>
                          <div className="text-sm text-gray-600">{phase.frequency}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-blue-600">{phase.concentration}%</div>
                          <div className="text-xs text-gray-500">concentration</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                    <div>
                      <h6 className="font-semibold text-yellow-800">Important Safety Notes</h6>
                      <ul className="text-sm text-yellow-700 mt-1 space-y-1">
                        <li>• Always patch test before increasing concentration</li>
                        <li>• Use sunscreen daily when using actives</li>
                        <li>• Stop if you experience severe irritation</li>
                        <li>• Don't mix multiple strong actives initially</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Routine Tracker Interface
  const renderRoutineTracker = () => {
    const handleSetupRoutine = () => {
      setTrackerStep('tracking');
      setRoutineData({
        morning: ['Cleanser', 'Vitamin C Serum', 'Moisturizer', 'SPF'],
        evening: ['Cleanser', 'Retinol', 'Moisturizer'],
        currentDay: 1,
        skinRating: null,
        notes: '',
      });
    };

    const handleLogDay = (rating: number, notes: string) => {
      setRoutineData({
        ...routineData,
        currentDay: routineData.currentDay + 1,
        skinRating: rating,
        notes: notes,
      });
    };

    if (trackerStep === 'setup') {
      return (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
              <Calendar className="w-8 h-8 text-indigo-600" />
            </div>
            <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
              Track Your Skincare Journey
            </h3>
            <p className="text-gray-600">
              Monitor your routine, track progress, and get personalized insights
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="p-6 border border-gray-200 rounded-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-yellow-600 font-semibold">AM</span>
                    </div>
                    <h4 className="font-semibold text-brand-charcoal">Morning Routine</h4>
                  </div>
                  <div className="space-y-2">
                    <input type="text" placeholder="Cleanser" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                    <input type="text" placeholder="Serum/Treatment" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                    <input type="text" placeholder="Moisturizer" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                    <input type="text" placeholder="SPF" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                  </div>
                </div>

                <div className="p-6 border border-gray-200 rounded-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-purple-600 font-semibold">PM</span>
                    </div>
                    <h4 className="font-semibold text-brand-charcoal">Evening Routine</h4>
                  </div>
                  <div className="space-y-2">
                    <input type="text" placeholder="Cleanser" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                    <input type="text" placeholder="Treatment/Active" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                    <input type="text" placeholder="Moisturizer" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                    <input type="text" placeholder="Face Oil (optional)" className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm" />
                  </div>
                </div>
              </div>

              <button onClick={handleSetupRoutine} className="btn-primary w-full">
                Start Tracking My Routine
              </button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
            <BarChart3 className="w-8 h-8 text-indigo-600" />
          </div>
          <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
            Day {routineData.currentDay} - How's Your Skin Today?
          </h3>
          <p className="text-gray-600">
            Rate your skin and log any observations
          </p>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="p-6 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg">
              <h4 className="font-semibold text-brand-charcoal mb-3">Morning Routine ✓</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                {routineData.morning.map((step: string, index: number) => (
                  <li key={index} className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    {step}
                  </li>
                ))}
              </ul>
            </div>

            <div className="p-6 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
              <h4 className="font-semibold text-brand-charcoal mb-3">Evening Routine</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                {routineData.evening.map((step: string, index: number) => (
                  <li key={index} className="flex items-center">
                    <Calendar className="w-4 h-4 text-purple-500 mr-2" />
                    {step}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="p-6 border border-gray-200 rounded-lg">
            <h4 className="font-semibold text-brand-charcoal mb-4">Rate Your Skin Today</h4>
            <div className="flex justify-center space-x-2 mb-4">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  onClick={() => handleLogDay(rating, '')}
                  className="w-12 h-12 rounded-full border-2 border-gray-200 hover:border-indigo-500 flex items-center justify-center font-semibold transition-colors"
                >
                  {rating}
                </button>
              ))}
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>Poor</span>
              <span>Excellent</span>
            </div>
          </div>

          <div className="p-6 bg-indigo-50 rounded-lg">
            <h4 className="font-semibold text-brand-charcoal mb-3">Progress Insights</h4>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-indigo-600">{routineData.currentDay}</div>
                <div className="text-sm text-gray-600">Days Tracked</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-indigo-600">4.2</div>
                <div className="text-sm text-gray-600">Avg Rating</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-indigo-600">85%</div>
                <div className="text-sm text-gray-600">Consistency</div>
              </div>
            </div>
          </div>

          <div className="flex space-x-4">
            <button className="btn-secondary flex-1">
              <Camera className="w-4 h-4 mr-2" />
              Add Progress Photo
            </button>
            <button className="btn-primary flex-1">
              View Full Report
            </button>
          </div>
        </div>
      </div>
    );
  };

  // pH Compatibility Tool Interface
  const renderPHCompatibilityTool = () => {

    const addProduct = () => {
      setProducts([...products, '']);
    };

    const updateProduct = (index: number, value: string) => {
      const newProducts = [...products];
      newProducts[index] = value;
      setProducts(newProducts);
    };

    const analyzePH = () => {
      // Mock pH analysis
      const mockResults = {
        products: products.filter(p => p.trim()),
        layeringOrder: [
          { product: 'Vitamin C Serum', ph: 3.5, order: 1, waitTime: '15 minutes' },
          { product: 'Niacinamide Serum', ph: 6.0, order: 2, waitTime: '5 minutes' },
          { product: 'Moisturizer', ph: 5.5, order: 3, waitTime: 'None' },
        ],
        warnings: ['Vitamin C and Niacinamide may reduce effectiveness if used too close together'],
        tips: ['Apply products from lowest to highest pH', 'Wait between acidic products for optimal absorption'],
      };
      setPHResults(mockResults);
    };

    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-pink-100 rounded-full mb-4">
            <TestTube className="w-8 h-8 text-pink-600" />
          </div>
          <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
            pH Compatibility Analysis
          </h3>
          <p className="text-gray-600">
            Optimize your routine with pH-based layering recommendations
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Enter Your Skincare Products
            </label>
            <div className="space-y-3">
              {products.map((product, index) => (
                <input
                  key={index}
                  type="text"
                  value={product}
                  onChange={(e) => updateProduct(index, e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                  placeholder={`Product ${index + 1} (e.g., Vitamin C Serum, Retinol Cream)`}
                />
              ))}
            </div>
            <button
              onClick={addProduct}
              className="mt-3 text-brand-teal hover:text-brand-teal-dark font-medium text-sm"
            >
              + Add Another Product
            </button>
          </div>

          <button
            onClick={analyzePH}
            disabled={products.filter(p => p.trim()).length < 2}
            className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Analyze pH Compatibility
          </button>

          {phResults && (
            <div className="mt-8 space-y-6">
              <div className="bg-gradient-to-r from-pink-50 to-pink-100 rounded-lg p-6">
                <h4 className="font-bold text-xl text-brand-charcoal mb-4">
                  Optimal Layering Order
                </h4>

                <div className="space-y-3 mb-6">
                  {phResults.layeringOrder.map((item: { order: number; product: string; ph: string; reason: string }, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-white rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-pink-500 text-white rounded-full flex items-center justify-center font-bold text-sm mr-3">
                          {item.order}
                        </div>
                        <div>
                          <div className="font-medium text-brand-charcoal">{item.product}</div>
                          <div className="text-sm text-gray-600">pH {item.ph}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-pink-600">Wait: {item.waitTime}</div>
                      </div>
                    </div>
                  ))}
                </div>

                {phResults.warnings.length > 0 && (
                  <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200 mb-4">
                    <div className="flex items-start">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                      <div>
                        <h6 className="font-semibold text-yellow-800 mb-1">Compatibility Warnings</h6>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          {phResults.warnings.map((warning: string, index: number) => (
                            <li key={index}>• {warning}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-start">
                    <Sparkles className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
                    <div>
                      <h6 className="font-semibold text-green-800 mb-1">Pro Tips</h6>
                      <ul className="text-sm text-green-700 space-y-1">
                        {phResults.tips.map((tip: string, index: number) => (
                          <li key={index}>• {tip}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const handleQuizAnswer = (question: string, answer: string | string[]) => {
    setQuizAnswers({
      ...quizAnswers,
      [question]: answer,
    });
    setCurrentStep(currentStep + 1);

    // If we've reached the end of the quiz, generate a routine
    if (currentStep === 4) {
      generateRoutine();
    }
  };

  const generateRoutine = () => {
    // This would normally be a more complex algorithm based on the answers
    // For now, we'll use a simplified version
    
    const morningRoutine = [
      'Gentle Cleanser',
      'Hydrating Toner',
    ];
    
    const eveningRoutine = [
      'Oil Cleanser (if wearing makeup)',
      'Water-Based Cleanser',
      'Treatment Serum',
    ];
    
    const weeklyTreatments = [
      'Gentle Exfoliation (1-2x per week)',
    ];
    
    // Add products based on skin type
    if (quizAnswers.skinType === 'dry') {
      morningRoutine.push('Hydrating Serum with Hyaluronic Acid');
      morningRoutine.push('Rich Moisturizer with Ceramides');
      eveningRoutine.push('Hydrating Serum');
      eveningRoutine.push('Rich Night Cream');
    } else if (quizAnswers.skinType === 'oily') {
      morningRoutine.push('Niacinamide Serum');
      morningRoutine.push('Oil-Free Gel Moisturizer');
      eveningRoutine.push('Salicylic Acid Treatment');
      eveningRoutine.push('Lightweight Moisturizer');
    } else if (quizAnswers.skinType === 'combination') {
      morningRoutine.push('Balancing Toner');
      morningRoutine.push('Lightweight Moisturizer');
      eveningRoutine.push('Niacinamide Serum');
      eveningRoutine.push('Balancing Moisturizer');
    } else if (quizAnswers.skinType === 'sensitive') {
      morningRoutine.push('Centella Asiatica Serum');
      morningRoutine.push('Fragrance-Free Moisturizer');
      eveningRoutine.push('Barrier Repair Serum');
      eveningRoutine.push('Soothing Night Cream');
    }
    
    // Add sunscreen to morning routine
    morningRoutine.push('Broad Spectrum SPF 30+');
    
    // Add treatments based on concerns
    if (quizAnswers.concerns.includes('aging')) {
      eveningRoutine.splice(3, 0, 'Retinol Serum (2-3x per week)');
      weeklyTreatments.push('Antioxidant Mask (1x per week)');
    }
    
    if (quizAnswers.concerns.includes('acne')) {
      eveningRoutine.splice(3, 0, 'Spot Treatment with Benzoyl Peroxide');
      weeklyTreatments.push('Clay Mask (1x per week)');
    }
    
    if (quizAnswers.concerns.includes('hyperpigmentation')) {
      morningRoutine.splice(2, 0, 'Vitamin C Serum');
      eveningRoutine.splice(3, 0, 'Alpha Arbutin or Tranexamic Acid Serum');
      weeklyTreatments.push('Brightening Mask (1x per week)');
    }
    
    if (quizAnswers.concerns.includes('dryness')) {
      morningRoutine.splice(2, 0, 'Hydrating Essence');
      eveningRoutine.push('Facial Oil');
      weeklyTreatments.push('Hydrating Mask (1-2x per week)');
    }
    
    // Adjust for sensitivity
    if (quizAnswers.sensitivity === 'very') {
      // Remove potentially irritating ingredients
      const filteredMorning = morningRoutine.filter(item => !item.includes('Vitamin C'));
      const filteredEvening = eveningRoutine.filter(item => !item.includes('Retinol') && !item.includes('Acid'));
      const filteredWeekly = weeklyTreatments.filter(item => !item.includes('Exfoliation'));
      
      // Add gentle alternatives
      filteredMorning.splice(2, 0, 'Centella Asiatica Serum');
      filteredEvening.splice(3, 0, 'Barrier Repair Serum');
      filteredWeekly.push('Oatmeal Mask (1x per week)');
      
      setRoutineResult({
        morning: filteredMorning,
        evening: filteredEvening,
        weekly: filteredWeekly,
      });
    } else {
      setRoutineResult({
        morning: morningRoutine,
        evening: eveningRoutine,
        weekly: weeklyTreatments,
      });
    }
  };

  const resetQuiz = () => {
    setQuizStarted(false);
    setCurrentStep(1);
    setQuizAnswers({
      skinType: '',
      concerns: [],
      sensitivity: '',
      budget: '',
    });
    setRoutineResult(null);
  };

  // Render the Routine Builder Quiz
  const renderRoutineBuilder = () => {
    if (!quizStarted) {
      return (
        <div className="bg-white rounded-2xl p-8 shadow-sm">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <Users className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
              Get Your Personalized Skincare Routine
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Answer a few questions about your skin and we'll create a customized routine 
              tailored to your unique needs and concerns.
            </p>
            <button 
              onClick={() => setQuizStarted(true)}
              className="btn-primary"
            >
              Start Quiz
            </button>
          </div>
          
          <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
            <h4 className="font-semibold text-blue-800 mb-3 flex items-center">
              <Sparkles className="w-5 h-5 mr-2" />
              What You'll Get
            </h4>
            <div className="grid md:grid-cols-3 gap-4 text-sm">
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <span className="font-medium text-blue-700">Morning Routine</span>
                <p className="text-blue-600">Step-by-step AM product recommendations</p>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <span className="font-medium text-blue-700">Evening Routine</span>
                <p className="text-blue-600">Customized PM regimen for your skin</p>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <span className="font-medium text-blue-700">Weekly Treatments</span>
                <p className="text-blue-600">Special care recommendations</p>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    if (routineResult) {
      return (
        <div className="bg-white rounded-2xl p-8 shadow-sm">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
              Your Personalized Skincare Routine
            </h3>
            <p className="text-gray-600">
              Based on your skin profile, here's your customized routine
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white">
                  <Sparkles className="w-5 h-5" />
                </div>
                <h4 className="text-lg font-semibold text-blue-800">Morning Routine</h4>
              </div>
              <ol className="space-y-3">
                {routineResult.morning.map((step, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-blue-800 font-medium text-sm flex-shrink-0">
                      {index + 1}
                    </div>
                    <div>
                      <p className="text-blue-800 font-medium">{step}</p>
                    </div>
                  </li>
                ))}
              </ol>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-6 border border-purple-200">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white">
                  <Zap className="w-5 h-5" />
                </div>
                <h4 className="text-lg font-semibold text-purple-800">Evening Routine</h4>
              </div>
              <ol className="space-y-3">
                {routineResult.evening.map((step, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-200 rounded-full flex items-center justify-center text-purple-800 font-medium text-sm flex-shrink-0">
                      {index + 1}
                    </div>
                    <div>
                      <p className="text-purple-800 font-medium">{step}</p>
                    </div>
                  </li>
                ))}
              </ol>
            </div>
          </div>
          
          <div className="bg-green-50 rounded-lg p-6 border border-green-200 mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white">
                <Shield className="w-5 h-5" />
              </div>
              <h4 className="text-lg font-semibold text-green-800">Weekly Treatments</h4>
            </div>
            <ul className="space-y-3">
              {routineResult.weekly.map((treatment, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-200 rounded-full flex items-center justify-center text-green-800 font-medium text-sm flex-shrink-0">
                    •
                  </div>
                  <div>
                    <p className="text-green-800 font-medium">{treatment}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200 mb-8">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-1 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-yellow-800 mb-2">Important Notes</h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Introduce new products one at a time, with 1-2 weeks between additions</li>
                  <li>• Patch test all new products before applying to your entire face</li>
                  <li>• Adjust routine based on how your skin responds</li>
                  <li>• Consistency is key - give products 6-8 weeks to show results</li>
                  <li>• Always wear sunscreen during the day, even indoors</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="text-center">
            <button 
              onClick={resetQuiz}
              className="btn-secondary"
            >
              Retake Quiz
            </button>
          </div>
        </div>
      );
    }
    
    // Quiz steps
    switch (currentStep) {
      case 1:
        return (
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                Step 1: What's your skin type?
              </h3>
              <p className="text-gray-600">
                Select the option that best describes your skin most of the time
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4 max-w-2xl mx-auto">
              <button 
                onClick={() => handleQuizAnswer('skinType', 'dry')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-left"
              >
                <h4 className="font-semibold text-brand-charcoal mb-2">Dry</h4>
                <p className="text-sm text-gray-600">Feels tight, flaky, or rough. Rarely gets oily, even in T-zone.</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('skinType', 'oily')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-left"
              >
                <h4 className="font-semibold text-brand-charcoal mb-2">Oily</h4>
                <p className="text-sm text-gray-600">Gets shiny throughout the day. Prone to enlarged pores and blackheads.</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('skinType', 'combination')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-left"
              >
                <h4 className="font-semibold text-brand-charcoal mb-2">Combination</h4>
                <p className="text-sm text-gray-600">Oily T-zone (forehead, nose, chin) but normal or dry cheeks.</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('skinType', 'sensitive')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-left"
              >
                <h4 className="font-semibold text-brand-charcoal mb-2">Sensitive</h4>
                <p className="text-sm text-gray-600">Easily irritated, reacts to many products with redness or stinging.</p>
              </button>
            </div>
          </div>
        );
        
      case 2:
        return (
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                Step 2: What are your main skin concerns?
              </h3>
              <p className="text-gray-600">
                Select all that apply (up to 3)
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-4 max-w-3xl mx-auto">
              <button 
                onClick={() => handleQuizAnswer('concerns', ['aging'])}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Anti-Aging</h4>
                <p className="text-xs text-gray-600 mt-1">Fine lines, wrinkles, firmness</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('concerns', ['acne'])}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Acne</h4>
                <p className="text-xs text-gray-600 mt-1">Breakouts, clogged pores</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('concerns', ['hyperpigmentation'])}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Hyperpigmentation</h4>
                <p className="text-xs text-gray-600 mt-1">Dark spots, uneven tone</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('concerns', ['dryness'])}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Dryness</h4>
                <p className="text-xs text-gray-600 mt-1">Dehydration, flakiness</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('concerns', ['redness'])}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Redness</h4>
                <p className="text-xs text-gray-600 mt-1">Inflammation, irritation</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('concerns', ['texture'])}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Texture</h4>
                <p className="text-xs text-gray-600 mt-1">Roughness, uneven surface</p>
              </button>
            </div>
          </div>
        );
        
      case 3:
        return (
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                Step 3: How sensitive is your skin?
              </h3>
              <p className="text-gray-600">
                Select the option that best describes your skin's reactivity
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-4 max-w-3xl mx-auto">
              <button 
                onClick={() => handleQuizAnswer('sensitivity', 'not')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Not Sensitive</h4>
                <p className="text-xs text-gray-600 mt-1">Can use most products without reaction</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('sensitivity', 'somewhat')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Somewhat Sensitive</h4>
                <p className="text-xs text-gray-600 mt-1">Occasionally reacts to certain ingredients</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('sensitivity', 'very')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Very Sensitive</h4>
                <p className="text-xs text-gray-600 mt-1">Reacts to many products, easily irritated</p>
              </button>
            </div>
          </div>
        );
        
      case 4:
        return (
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                Step 4: What's your skincare budget?
              </h3>
              <p className="text-gray-600">
                This helps us recommend products within your price range
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-4 max-w-3xl mx-auto">
              <button 
                onClick={() => handleQuizAnswer('budget', 'budget')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Budget-Friendly</h4>
                <p className="text-xs text-gray-600 mt-1">Drugstore and affordable options</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('budget', 'mid')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Mid-Range</h4>
                <p className="text-xs text-gray-600 mt-1">Mix of affordable and premium products</p>
              </button>
              
              <button 
                onClick={() => handleQuizAnswer('budget', 'premium')}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 transition-colors duration-200 text-center"
              >
                <h4 className="font-semibold text-brand-charcoal">Premium</h4>
                <p className="text-xs text-gray-600 mt-1">High-end and luxury skincare</p>
              </button>
            </div>
          </div>
        );
        
      default:
        return (
          <div className="bg-white rounded-2xl p-8 shadow-sm text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <Sparkles className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
              Generating Your Personalized Routine...
            </h3>
            <p className="text-gray-600">
              Please wait while we analyze your skin profile and create your custom recommendations.
            </p>
          </div>
        );
    }
  };

  // Render the Compatibility Checker
  const renderCompatibilityChecker = () => {
    return (
      <div className="bg-white rounded-2xl p-8 shadow-sm">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
            Ingredient Compatibility Checker
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Check if your skincare ingredients work well together or if there are potential conflicts in your routine.
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto">
          <div className="mb-6">
            <label htmlFor="ingredients" className="block text-sm font-medium text-gray-700 mb-2">
              Enter your ingredients or products (one per line)
            </label>
            <textarea
              id="ingredients"
              rows={5}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              placeholder="Example:
Vitamin C Serum
Niacinamide 10%
Retinol 0.5%
Hyaluronic Acid"
            ></textarea>
          </div>
          
          <button className="btn-primary w-full">
            Check Compatibility
          </button>
          
          <div className="mt-8 bg-gray-50 rounded-lg p-6">
            <h4 className="font-semibold text-brand-charcoal mb-4">Sample Results</h4>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium">Vitamin C + Niacinamide</span>
                </div>
                <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">Compatible</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  <span className="font-medium">Retinol + AHA/BHA</span>
                </div>
                <span className="text-sm bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Use with Caution</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <span className="font-medium">Retinol + Benzoyl Peroxide</span>
                </div>
                <span className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded-full">Not Compatible</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title={seoTitle}
        description={seoDescription}
        canonicalUrl={canonicalUrl}
        keywords={`${tool.title.toLowerCase()}, skincare tools, ${slug}, skincare calculator, ingredient checker`}
        ogType="website"
        pageType="tool"
      />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-4">
          <nav aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link to="/" className="text-gray-500 hover:text-brand-teal">Home</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link to="/tools" className="text-gray-500 hover:text-brand-teal">Tools</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-brand-charcoal font-medium">{tool.title}</li>
            </ol>
          </nav>
          <Link
            to="/tools"
            className="inline-flex items-center space-x-2 text-brand-teal hover:text-brand-teal-dark transition-colors duration-200 mt-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Tools</span>
          </Link>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white">
        <div className="container-custom py-12">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-4 mb-6">
              <div className={`w-16 h-16 ${tool.color} rounded-xl flex items-center justify-center`}>
                <tool.icon className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold text-brand-charcoal mb-2">
                  {tool.title}
                </h1>
                <div className="flex items-center space-x-4 text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{tool.users} users</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span>{tool.rating}/5</span>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-lg text-gray-600 leading-relaxed mb-8">
              {tool.longDescription}
            </p>
          </div>
        </div>
      </div>

      {/* Tool Interface */}
      <div className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {/* Render the appropriate tool interface based on the slug */}
            {slug === 'routine-builder-quiz' && renderRoutineBuilder()}
            {slug === 'ingredient-compatibility-checker' && renderCompatibilityChecker()}
            {slug === 'ingredient-analyzer' && renderIngredientAnalyzer()}
            {slug === 'concentration-calculator' && renderConcentrationCalculator()}
            {slug === 'routine-tracker' && renderRoutineTracker()}
            {slug === 'ph-compatibility-tool' && renderPHCompatibilityTool()}
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Key Features
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {tool.features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-brand-teal/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="w-5 h-5 text-brand-teal" />
                  </div>
                  <div>
                    <p className="text-gray-700">{feature}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* How to Use */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              How to Use
            </h2>
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="space-y-6">
                {tool.howToUse.map((step, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-brand-teal rounded-full flex items-center justify-center text-white font-bold flex-shrink-0">
                      {index + 1}
                    </div>
                    <div className="pt-2">
                      <p className="text-gray-700">{step}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Reviews */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              User Reviews
            </h2>
            <div className="space-y-6">
              {tool.reviews.map((review, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-gray-500 font-medium">{review.name.charAt(0)}</span>
                      </div>
                      <span className="font-medium text-brand-charcoal">{review.name}</span>
                    </div>
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`w-4 h-4 ${i < review.rating ? 'text-yellow-500 fill-current' : 'text-gray-300'}`} 
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-600">{review.comment}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Other Tools */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Explore Other Tools
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {Object.entries(tools)
                .filter(([key]) => key !== slug)
                .map(([key, otherTool]) => (
                  <Link
                    key={key}
                    to={`/tools/${key}`}
                    className="card p-6 hover:scale-105 transition-all duration-200 group"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className={`inline-flex items-center justify-center w-12 h-12 ${otherTool.color} rounded-lg group-hover:scale-110 transition-transform duration-200`}>
                        <otherTool.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm text-gray-600">{otherTool.rating}</span>
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-semibold text-brand-charcoal mb-3 group-hover:text-brand-teal transition-colors duration-200">
                      {otherTool.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-4 text-sm">
                      {otherTool.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {otherTool.users} users
                      </span>
                      <ArrowRight className="w-5 h-5 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                    </div>
                  </Link>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolDetailPage;