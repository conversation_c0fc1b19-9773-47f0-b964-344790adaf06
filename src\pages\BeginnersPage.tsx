import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { BookOpen, GraduationCap, Users, Clock, ArrowRight, Star } from 'lucide-react';
import SkincareByAgeGuide from '../components/infographics/SkincareByAgeGuide';
import SkincareProductShelfLife from '../components/infographics/SkincareProductShelfLife';
import MetaTags from '../components/SEO/MetaTags';

const BeginnersPage: React.FC = () => {
  const guides = [
    {
      slug: 'skincare-101',
      title: 'Skincare 101: Your First Routine',
      description: 'Master the basics with our comprehensive guide to building your first skincare routine. Perfect for absolute beginners.',
      readTime: '8 min read',
      difficulty: 'Beginner',
      color: 'bg-category-green',
      isPopular: true,
      topics: ['Cleansing', 'Moisturizing', 'Sun Protection', 'Product Order'],
    },
    {
      slug: 'ingredient-dictionary',
      title: 'Ingredient Dictionary',
      description: 'Decode skincare labels with our beginner-friendly ingredient dictionary. No chemistry degree required.',
      readTime: '12 min read',
      difficulty: 'Beginner',
      color: 'bg-category-blue',
      isPopular: true,
      topics: ['Common Ingredients', 'INCI Names', 'What to Avoid', 'Label Reading'],
    },
    {
      slug: 'how-to-read-labels',
      title: 'How to Read Skincare Labels',
      description: 'Learn to decode ingredient lists, understand concentrations, and identify marketing claims vs. facts.',
      readTime: '10 min read',
      difficulty: 'Beginner',
      color: 'bg-category-lavender',
      isPopular: false,
      topics: ['Ingredient Order', 'Concentrations', 'Marketing Claims', 'Red Flags'],
    },
    {
      slug: 'skin-types-guide',
      title: 'Understanding Your Skin Type',
      description: 'Identify your skin type and learn how to choose products that work best for your unique needs.',
      readTime: '6 min read',
      difficulty: 'Beginner',
      color: 'bg-category-pink',
      isPopular: true,
      topics: ['Skin Types', 'Product Selection', 'Common Mistakes', 'Seasonal Changes'],
    },
    {
      slug: 'routine-building',
      title: 'Building Your First Routine',
      description: 'Step-by-step guide to creating an effective skincare routine without overwhelming your skin.',
      readTime: '15 min read',
      difficulty: 'Beginner',
      color: 'bg-category-yellow',
      isPopular: false,
      topics: ['Product Order', 'Frequency', 'Patch Testing', 'Gradual Introduction'],
    },
    {
      slug: 'common-mistakes',
      title: 'Common Beginner Mistakes',
      description: 'Avoid the most common skincare mistakes that beginners make and learn how to fix them.',
      readTime: '7 min read',
      difficulty: 'Beginner',
      color: 'bg-category-orange',
      isPopular: true,
      topics: ['Over-exfoliation', 'Product Mixing', 'Unrealistic Expectations', 'Quick Fixes'],
    },
  ];

  const quickFacts = [
    {
      icon: Users,
      stat: '50K+',
      label: 'Beginners Helped',
    },
    {
      icon: BookOpen,
      stat: '25+',
      label: 'Beginner Guides',
    },
    {
      icon: GraduationCap,
      stat: '5',
      label: 'Learning Levels',
    },
  ];

  const learningPaths = [
    {
      title: 'Complete Beginner',
      description: 'Never used skincare before? Start here with the absolute basics.',
      guides: ['Skincare 101', 'Understanding Your Skin Type', 'Building Your First Routine'],
      duration: '2-3 weeks',
      color: 'bg-green-100 border-green-200',
    },
    {
      title: 'Label Reader',
      description: 'Learn to decode ingredients and make informed product choices.',
      guides: ['Ingredient Dictionary', 'How to Read Labels', 'Common Mistakes'],
      duration: '1-2 weeks',
      color: 'bg-blue-100 border-blue-200',
    },
    {
      title: 'Routine Builder',
      description: 'Ready to create a more advanced routine with active ingredients.',
      guides: ['Building Your First Routine', 'Active Ingredients 101', 'Layering Guide'],
      duration: '3-4 weeks',
      color: 'bg-purple-100 border-purple-200',
    },
  ];

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Beginner's Skincare Hub - Start Your Skincare Journey"
        description="Complete beginner's guide to skincare. Learn the basics, understand your skin type, and build your first routine with expert guidance and science-backed advice."
        keywords="skincare for beginners, skincare basics, first skincare routine, skin type guide, beginner skincare tips, skincare 101"
        canonicalUrl="https://www.skincarecompass.com/beginners"
      />
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-12">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
              <GraduationCap className="w-8 h-8 text-brand-teal" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Beginner's Hub
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Start your skincare journey with confidence. Our beginner-friendly guides make 
              complex skincare science simple and actionable.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-6 max-w-2xl mx-auto">
            {quickFacts.map((fact, index) => (
              <div key={index} className="text-center animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="inline-flex items-center justify-center w-12 h-12 bg-brand-teal/10 rounded-full mb-3">
                  <fact.icon className="w-6 h-6 text-brand-teal" />
                </div>
                <div className="text-2xl font-bold text-brand-charcoal">{fact.stat}</div>
                <div className="text-gray-600 text-sm">{fact.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Skincare By Age Guide */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <SkincareByAgeGuide />
        </div>
      </div>

      {/* Learning Paths */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-brand-charcoal mb-12 text-center">
            Choose Your Learning Path
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {learningPaths.map((path, index) => (
              <div
                key={index}
                className={`card p-6 border-2 ${path.color} hover:scale-105 transition-all duration-200 animate-slide-up`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <h3 className="text-xl font-semibold text-brand-charcoal mb-3">
                  {path.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {path.description}
                </p>
                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-500 mb-2">Includes:</div>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {path.guides.map((guide, idx) => (
                      <li key={idx} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-brand-teal rounded-full"></div>
                        <span>{guide}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    <Clock className="w-4 h-4 inline mr-1" />
                    {path.duration}
                  </span>
                  <ArrowRight className="w-5 h-5 text-brand-teal" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Product Shelf Life Guide */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <SkincareProductShelfLife />
        </div>
      </div>

      {/* All Guides */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-brand-charcoal mb-12 text-center">
            All Beginner Guides
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {guides.map((guide, index) => (
              <Link
                key={guide.slug}
                to={`/beginners/${guide.slug}`}
                className="card p-6 hover:scale-105 transition-all duration-200 group animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center justify-between mb-4">
                  <span className={`text-xs font-medium text-white px-3 py-1 rounded-full ${guide.color}`}>
                    {guide.difficulty}
                  </span>
                  <div className="flex items-center space-x-2">
                    {guide.isPopular && (
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-xs text-gray-500">Popular</span>
                      </div>
                    )}
                    <div className="flex items-center text-gray-500 text-sm">
                      <Clock className="w-4 h-4 mr-1" />
                      {guide.readTime}
                    </div>
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-brand-charcoal mb-3 group-hover:text-brand-teal transition-colors duration-200">
                  {guide.title}
                </h3>

                <p className="text-gray-600 mb-4 leading-relaxed text-sm">
                  {guide.description}
                </p>

                <div className="space-y-3">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    Topics Covered
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {guide.topics.map((topic, idx) => (
                      <span
                        key={idx}
                        className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-md"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="mt-4 flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    Start reading →
                  </span>
                  <ArrowRight className="w-4 h-4 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
              Still Have Questions?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join our beginner-friendly community where you can ask questions, share experiences, 
              and learn from others on the same journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/community" className="btn-primary">
                Join Community
              </Link>
              <Link to="/tools/routine-builder-quiz" className="btn-secondary">
                Take Routine Quiz
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BeginnersPage;