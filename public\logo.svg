<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="90" fill="url(#gradient)" />
  
  <!-- Compass needle -->
  <g transform="translate(100,100)">
    <!-- North pointer -->
    <polygon points="0,-60 -8,-20 8,-20" fill="white" />
    <!-- South pointer -->
    <polygon points="0,60 -8,20 8,20" fill="white" opacity="0.7" />
    <!-- East pointer -->
    <polygon points="60,0 20,-8 20,8" fill="white" opacity="0.7" />
    <!-- West pointer -->
    <polygon points="-60,0 -20,-8 -20,8" fill="white" opacity="0.7" />
    
    <!-- Center circle -->
    <circle cx="0" cy="0" r="12" fill="white" />
    <circle cx="0" cy="0" r="6" fill="url(#gradient)" />
  </g>
  
  <!-- Compass ring -->
  <circle cx="100" cy="100" r="75" fill="none" stroke="white" stroke-width="2" opacity="0.5" />
  <circle cx="100" cy="100" r="65" fill="none" stroke="white" stroke-width="1" opacity="0.3" />
</svg>
