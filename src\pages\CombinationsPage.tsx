import React, { useState } from 'react';
import { Search, CheckCircle, XCircle, AlertTriangle, TrendingUp } from 'lucide-react';
import { Link } from 'react-router-dom';
import RoutineLayeringGuide from '../components/infographics/RoutineLayeringGuide';
import MetaTags from '../components/SEO/MetaTags';

const CombinationsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState('popular');

  const combinations = [
    {
      slug: 'vitamin-c-ferulic-acid-vitamin-e',
      ingredients: ['Vitamin C', 'Ferulic Acid', 'Vitamin E'],
      status: 'excellent',
      effect: 'Antioxidant powerhouse',
      description: 'This classic combination stabilizes Vitamin C and provides enhanced antioxidant protection.',
      benefits: ['Enhanced stability', 'Superior antioxidant protection', 'Brightening effect'],
      popularity: 95,
      isTrending: true,
    },
    {
      slug: 'niacinamide-zinc',
      ingredients: ['Niacinamide', 'Zinc'],
      status: 'excellent',
      effect: 'Oil control and pore refinement',
      description: 'Niacinamide and zinc work together to regulate sebum production and minimize pores.',
      benefits: ['Oil control', 'Pore minimizing', 'Anti-inflammatory'],
      popularity: 88,
      isTrending: false,
    },
    {
      slug: 'retinol-peptides',
      ingredients: ['Retinol', 'Peptides'],
      status: 'excellent',
      effect: 'Advanced anti-aging',
      description: 'Retinol stimulates cell turnover while peptides support collagen production.',
      benefits: ['Collagen synthesis', 'Cell renewal', 'Wrinkle reduction'],
      popularity: 82,
      isTrending: true,
    },
    {
      slug: 'aha-bha-combination',
      ingredients: ['AHA', 'BHA'],
      status: 'caution',
      effect: 'Deep exfoliation',
      description: 'Powerful exfoliation but requires careful introduction and monitoring.',
      benefits: ['Deep exfoliation', 'Improved texture', 'Requires gradual introduction'],
      popularity: 76,
      isTrending: false,
    },
    {
      slug: 'retinol-vitamin-c',
      ingredients: ['Retinol', 'Vitamin C'],
      status: 'avoid',
      effect: 'pH conflict',
      description: 'Different pH requirements and potential for irritation make this challenging.',
      benefits: ['Use separately', 'AM/PM routine', 'Alternate days'],
      popularity: 45,
      isTrending: false,
    },
  ];

  const statusOptions = [
    { value: 'all', label: 'All Combinations' },
    { value: 'excellent', label: 'Excellent Synergy' },
    { value: 'caution', label: 'Use With Caution' },
    { value: 'avoid', label: 'Avoid Combining' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'caution':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'avoid':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return CheckCircle;
      case 'caution':
        return AlertTriangle;
      case 'avoid':
        return XCircle;
      default:
        return CheckCircle;
    }
  };

  const filteredCombinations = combinations.filter(combo => {
    const matchesSearch = combo.ingredients.some(ingredient =>
      ingredient.toLowerCase().includes(searchTerm.toLowerCase())
    ) || combo.effect.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || combo.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const sortedCombinations = [...filteredCombinations].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.popularity - a.popularity;
      case 'alphabetical':
        return a.ingredients[0].localeCompare(b.ingredients[0]);
      case 'status': {
        const statusOrder = { excellent: 3, caution: 2, avoid: 1 };
        return statusOrder[b.status as keyof typeof statusOrder] - statusOrder[a.status as keyof typeof statusOrder];
      }
      default:
        return 0;
    }
  });

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Skincare Ingredient Combinations Guide - Safe & Effective Pairings"
        description="Learn which skincare ingredients work well together and which to avoid. Expert-backed combination guides for building effective, safe skincare routines."
        keywords="skincare combinations, ingredient compatibility, skincare layering, ingredient interactions, skincare routine guide, safe ingredient mixing"
        canonicalUrl="https://www.skincarecompass.com/combinations"
      />
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Combination Guides
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover which ingredients work together synergistically and which combinations 
              to approach with caution for optimal skincare results.
            </p>
          </div>

          {/* Search and Filters */}
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search combinations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
              </div>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                >
                  <option value="popular">Most Popular</option>
                  <option value="alphabetical">Alphabetical</option>
                  <option value="status">By Status</option>
                </select>
              </div>
              <p className="text-gray-600">
                Showing {sortedCombinations.length} combinations
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Layering Guide */}
      <div className="container-custom py-12">
        <RoutineLayeringGuide />
      </div>

      {/* Results */}
      <div className="container-custom py-12">
        <div className="grid lg:grid-cols-2 gap-8">
          {sortedCombinations.map((combo, index) => {
            const StatusIcon = getStatusIcon(combo.status);
            return (
              <Link
                key={combo.slug}
                to={`/combinations/${combo.slug}`}
                className={`card p-6 border-2 ${getStatusColor(combo.status)} hover:scale-105 transition-all duration-200 group animate-slide-up`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <StatusIcon className="w-6 h-6" />
                    <div>
                      <h3 className="font-semibold text-lg">
                        {combo.ingredients.join(' + ')}
                      </h3>
                      <p className="text-sm font-medium">{combo.effect}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {combo.isTrending && (
                      <TrendingUp className="w-4 h-4 text-orange-500" />
                    )}
                    <span className="text-sm text-gray-500">{combo.popularity}%</span>
                  </div>
                </div>

                <p className="text-gray-600 mb-4 leading-relaxed">
                  {combo.description}
                </p>

                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-700">Key Points:</div>
                  <div className="flex flex-wrap gap-2">
                    {combo.benefits.map((benefit, idx) => (
                      <span
                        key={idx}
                        className="text-xs bg-white px-3 py-1 rounded-full border border-gray-200"
                      >
                        {benefit}
                      </span>
                    ))}
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {sortedCombinations.length === 0 && (
          <div className="text-center py-12">
            <CheckCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No combinations found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>

      {/* CTA Section */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
              Need Personalized Combination Advice?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Our advanced combination checker analyzes your entire routine and provides 
              personalized recommendations based on your skin type and concerns.
            </p>
            <Link to="/tools/ingredient-compatibility-checker" className="btn-primary">
              Try Combination Checker
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CombinationsPage;