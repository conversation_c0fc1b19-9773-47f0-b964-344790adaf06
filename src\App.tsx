import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';

// Page Components
import HomePage from './pages/HomePage';
import IngredientsPage from './pages/IngredientsPage';
import IngredientDetailPage from './pages/IngredientDetailPage';
import CombinationsPage from './pages/CombinationsPage';
import CombinationDetailPage from './pages/CombinationDetailPage';
import BeginnersPage from './pages/BeginnersPage';
import BeginnerGuidePage from './pages/BeginnerGuidePage';
import ToolsPage from './pages/ToolsPage';
import ToolDetailPage from './pages/ToolDetailPage';
import BlogPage from './pages/BlogPage';
import BlogPostPage from './pages/BlogPostPage';
import AboutPage from './pages/AboutPage';
import FAQPage from './pages/FAQPage';
import InfographicsPage from './pages/InfographicsPage';
import PrivacyPolicyPage from './pages/PrivacyPolicyPage';
import TermsOfServicePage from './pages/TermsOfServicePage';
import IngredientSafetyPage from './pages/IngredientSafetyPage';
import NotFoundPage from './pages/NotFoundPage';

// Scroll to top component
const ScrollToTop: React.FC = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

function App() {
  return (
    <Router>
      <ScrollToTop />
      <div className="min-h-screen bg-brand-off-white">
        <Header />
        <main>
          <Routes>
            {/* Main Pages */}
            <Route path="/" element={<HomePage />} />
            <Route path="/ingredients" element={<IngredientsPage />} />
            <Route path="/combinations" element={<CombinationsPage />} />
            <Route path="/beginners" element={<BeginnersPage />} />
            <Route path="/tools" element={<ToolsPage />} />
            <Route path="/blog" element={<BlogPage />} />
            <Route path="/infographics" element={<InfographicsPage />} />
            
            {/* Ingredient Detail Pages */}
            <Route path="/ingredients/:slug" element={<IngredientDetailPage />} />
            
            {/* Combination Detail Pages */}
            <Route path="/combinations/:slug" element={<CombinationDetailPage />} />
            
            {/* Beginner Guide Pages */}
            <Route path="/beginners/:slug" element={<BeginnerGuidePage />} />
            
            {/* Tool Pages */}
            <Route path="/tools/:slug" element={<ToolDetailPage />} />
            
            {/* Blog Post Pages */}
            <Route path="/blog/:slug" element={<BlogPostPage />} />
            
            {/* Static Pages */}
            <Route path="/about" element={<AboutPage />} />
            <Route path="/faq" element={<FAQPage />} />
            <Route path="/ingredient-safety" element={<IngredientSafetyPage />} />
            <Route path="/privacy" element={<PrivacyPolicyPage />} />
            <Route path="/terms" element={<TermsOfServicePage />} />
            
            {/* 404 Page */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;