import React, { useState } from 'react';
import { AlertTriangle, CheckCircle, XCircle, Lightbulb, Search } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const SkincareMythsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Myths' },
    { id: 'ingredients', name: 'Ingredients' },
    { id: 'routines', name: 'Routines' },
    { id: 'acne', name: 'Acne' },
    { id: 'aging', name: 'Anti-Aging' },
    { id: 'sun-protection', name: 'Sun Protection' },
  ];

  const myths = [
    {
      id: 1,
      category: 'ingredients',
      myth: 'Natural ingredients are always safer than synthetic ones',
      truth: 'Natural doesn\'t automatically mean safer. Many natural ingredients can cause allergic reactions or irritation, while many synthetic ingredients are gentler and more stable. Poison ivy is natural, but that doesn\'t make it safe for your skin.',
      explanation: 'The safety of an ingredient depends on its molecular structure, concentration, and how it interacts with your skin - not whether it comes from nature or a lab. Many synthetic ingredients are actually identical to their natural counterparts at the molecular level.',
      evidence: 'Dermatological studies show that synthetic ingredients like niacinamide and hyaluronic acid often have better safety profiles than some natural extracts.',
      isPopular: true,
    },
    {
      id: 2,
      category: 'routines',
      myth: 'You need a 10-step skincare routine to have good skin',
      truth: 'A simple 3-4 step routine (cleanser, treatment, moisturizer, sunscreen) is often more effective than a complex routine. More products increase the risk of irritation and make it harder to identify what\'s working.',
      explanation: 'Your skin barrier can only absorb so much at once. Overloading it with products can cause irritation, clogged pores, and actually worsen skin conditions. Consistency with a few effective products beats complexity.',
      evidence: 'Dermatologists consistently recommend starting with basics and adding products gradually. Studies show that routine adherence decreases as complexity increases.',
      isPopular: true,
    },
    {
      id: 3,
      category: 'acne',
      myth: 'Oily skin doesn\'t need moisturizer',
      truth: 'All skin types need moisturizer, including oily skin. Skipping moisturizer can actually make oily skin worse by triggering increased oil production as your skin tries to compensate for dehydration.',
      explanation: 'Oily skin can still be dehydrated (lacking water). When you skip moisturizer, your skin may produce more oil to protect itself, leading to a cycle of increased oiliness and potential breakouts.',
      evidence: 'Clinical studies show that using appropriate moisturizers can actually reduce sebum production in oily skin types over time.',
      isPopular: true,
    },
    {
      id: 4,
      category: 'acne',
      myth: 'Scrubbing your face harder will clear acne faster',
      truth: 'Aggressive scrubbing irritates the skin and can worsen acne by causing micro-tears and inflammation. Gentle cleansing and chemical exfoliation are much more effective for acne treatment.',
      explanation: 'Acne is caused by clogged pores, bacteria, and inflammation - not surface dirt. Harsh scrubbing damages the skin barrier, increases inflammation, and can spread bacteria to other areas of the face.',
      evidence: 'Dermatological research consistently shows that gentle skincare approaches are more effective for acne management than aggressive treatments.',
      isPopular: false,
    },
    {
      id: 5,
      category: 'sun-protection',
      myth: 'You don\'t need sunscreen on cloudy days',
      truth: 'Up to 80% of UV rays can penetrate clouds. UVA rays, which cause aging and skin damage, are present year-round and can penetrate glass and clouds.',
      explanation: 'Clouds provide minimal UV protection. UVA rays maintain consistent intensity throughout the day and year, regardless of weather conditions. This is why you can get sunburned on overcast days.',
      evidence: 'UV measurements show significant UV exposure even on completely overcast days, with UVA levels remaining particularly high.',
      isPopular: true,
    },
    {
      id: 6,
      category: 'aging',
      myth: 'Anti-aging products don\'t work until you\'re older',
      truth: 'Prevention is more effective than correction. Starting anti-aging ingredients like vitamin C and retinol in your 20s can prevent damage that would be much harder to reverse later.',
      explanation: 'Skin aging is a cumulative process that starts in your 20s. Early intervention with proven ingredients can maintain collagen production, prevent sun damage, and keep skin healthier longer.',
      evidence: 'Long-term studies show that people who start using retinoids and sunscreen in their 20s have significantly better skin quality in their 40s and beyond.',
      isPopular: false,
    },
    {
      id: 7,
      category: 'ingredients',
      myth: 'Expensive products work better than drugstore ones',
      truth: 'Price doesn\'t determine effectiveness. Many drugstore products contain the same active ingredients as luxury brands at similar concentrations. What matters is the formulation and ingredient quality.',
      explanation: 'The cost of skincare products often reflects packaging, marketing, and brand positioning rather than ingredient efficacy. Many affordable products are formulated by the same labs that create luxury products.',
      evidence: 'Comparative studies of active ingredients show no significant difference in efficacy between expensive and affordable products with similar formulations.',
      isPopular: true,
    },
    {
      id: 8,
      category: 'routines',
      myth: 'You should change your skincare routine frequently',
      truth: 'Consistency is key in skincare. Constantly changing products prevents you from seeing results and can irritate your skin. Most products need 4-6 weeks to show benefits.',
      explanation: 'Skin cell turnover takes about 28 days, and many active ingredients need multiple cycles to show visible improvements. Frequent changes also make it impossible to identify which products are actually working.',
      evidence: 'Clinical trials for skincare products typically run 8-12 weeks because shorter periods don\'t allow enough time to see meaningful results.',
      isPopular: false,
    },
    {
      id: 9,
      category: 'acne',
      myth: 'Toothpaste can cure pimples',
      truth: 'Toothpaste can actually worsen acne and cause chemical burns. It contains ingredients like sodium lauryl sulfate and fluoride that are too harsh for facial skin and can cause severe irritation.',
      explanation: 'While toothpaste might temporarily dry out a pimple, it can cause inflammation, chemical burns, and post-inflammatory hyperpigmentation. Proper acne treatments are much safer and more effective.',
      evidence: 'Dermatologists report numerous cases of chemical burns and scarring from toothpaste application to acne.',
      isPopular: false,
    },
    {
      id: 10,
      category: 'sun-protection',
      myth: 'Higher SPF means you can stay in the sun longer',
      truth: 'SPF measures protection strength, not duration. SPF 30 blocks 97% of UVB rays, while SPF 50 blocks 98%. The difference is minimal, and all sunscreens need reapplication every 2 hours.',
      explanation: 'SPF is calculated based on how long it takes skin to burn compared to unprotected skin. However, sunscreen breaks down over time due to UV exposure, sweat, and friction, regardless of SPF level.',
      evidence: 'Studies show that most people apply insufficient sunscreen and don\'t reapply frequently enough, making SPF level less important than proper application and reapplication.',
      isPopular: true,
    },
    {
      id: 11,
      category: 'ingredients',
      myth: 'You can\'t use vitamin C and retinol together',
      truth: 'While it\'s often recommended to use them at different times for optimal effectiveness, they can be used together if your skin tolerates it. The key is proper formulation and gradual introduction.',
      explanation: 'The concern about combining vitamin C and retinol is based on pH differences and potential irritation, not dangerous interactions. Many people successfully use both in their routines.',
      evidence: 'Recent formulation advances have created stable combinations of vitamin C and retinol, and many dermatologists now recommend using them at different times rather than avoiding the combination entirely.',
      isPopular: false,
    },
    {
      id: 12,
      category: 'aging',
      myth: 'Facial exercises can replace anti-aging products',
      truth: 'While facial exercises may provide some temporary tightening, they cannot replace the proven benefits of ingredients like retinoids, vitamin C, and sunscreen for preventing and treating signs of aging.',
      explanation: 'Skin aging involves collagen breakdown, sun damage, and cellular changes that cannot be addressed through muscle exercises alone. Repetitive facial movements can actually contribute to wrinkle formation.',
      evidence: 'Scientific evidence for facial exercises is limited and short-term, while decades of research support the effectiveness of topical anti-aging ingredients.',
      isPopular: false,
    },
  ];

  const filteredMyths = myths.filter(myth => {
    const matchesSearch = myth.myth.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         myth.truth.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || myth.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const popularMyths = myths.filter(myth => myth.isPopular);

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Skincare Myths Debunked - Separating Fact from Fiction | Expert Analysis"
        description="Discover the truth behind common skincare myths. Our experts debunk 20+ popular misconceptions about ingredients, routines, and treatments with scientific evidence."
        keywords="skincare myths, skincare facts, skincare misconceptions, beauty myths debunked, skincare truth, evidence-based skincare, skincare science facts"
        canonicalUrl="https://www.skincarecompass.com/myths"
      />

      {/* Hero Section */}
      <div className="section-padding bg-gradient-to-br from-red-50 to-orange-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-6">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-6">
              Skincare Myths Debunked
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed mb-8">
              Separate fact from fiction with our expert analysis of common skincare myths. 
              Get the truth backed by scientific evidence and protect yourself from harmful misinformation.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">{myths.length}+</div>
                <div className="text-sm text-gray-600">Myths Debunked</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">6</div>
                <div className="text-sm text-gray-600">Categories</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">{popularMyths.length}</div>
                <div className="text-sm text-gray-600">Popular Myths</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">100%</div>
                <div className="text-sm text-gray-600">Evidence-Based</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search myths and facts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Myths */}
      {searchTerm === '' && selectedCategory === 'all' && (
        <div className="section-padding bg-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold text-brand-charcoal mb-8 flex items-center">
                <AlertTriangle className="w-6 h-6 text-red-600 mr-2" />
                Most Common Myths
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {popularMyths.slice(0, 4).map((myth) => (
                  <div key={myth.id} className="bg-red-50 border border-red-200 rounded-lg p-6">
                    <div className="flex items-start space-x-3 mb-4">
                      <XCircle className="w-6 h-6 text-red-600 flex-shrink-0 mt-1" />
                      <div>
                        <h3 className="font-bold text-red-800 mb-2">MYTH:</h3>
                        <p className="text-red-700 font-medium">{myth.myth}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
                      <div>
                        <h3 className="font-bold text-green-800 mb-2">TRUTH:</h3>
                        <p className="text-green-700">{myth.truth}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* All Myths */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-brand-charcoal mb-8">
              {searchTerm || selectedCategory !== 'all' ? 'Search Results' : 'All Myths Debunked'}
              <span className="text-gray-500 font-normal ml-2">({filteredMyths.length} myths)</span>
            </h2>
            
            <div className="space-y-8">
              {filteredMyths.map((myth) => (
                <div key={myth.id} className="bg-white rounded-2xl p-8 shadow-sm">
                  {/* Myth */}
                  <div className="flex items-start space-x-4 mb-6">
                    <div className="bg-red-100 p-2 rounded-lg">
                      <XCircle className="w-6 h-6 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-red-800 mb-2">MYTH</h3>
                      <p className="text-red-700 font-medium text-lg">{myth.myth}</p>
                    </div>
                  </div>

                  {/* Truth */}
                  <div className="flex items-start space-x-4 mb-6">
                    <div className="bg-green-100 p-2 rounded-lg">
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-green-800 mb-2">TRUTH</h3>
                      <p className="text-green-700 font-medium text-lg">{myth.truth}</p>
                    </div>
                  </div>

                  {/* Explanation */}
                  <div className="flex items-start space-x-4 mb-6">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <Lightbulb className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-blue-800 mb-2">WHY THIS MATTERS</h3>
                      <p className="text-blue-700">{myth.explanation}</p>
                    </div>
                  </div>

                  {/* Evidence */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Scientific Evidence:</h4>
                    <p className="text-gray-600 text-sm">{myth.evidence}</p>
                  </div>

                  {/* Category Badge */}
                  <div className="mt-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      myth.category === 'ingredients' ? 'bg-purple-100 text-purple-700' :
                      myth.category === 'routines' ? 'bg-blue-100 text-blue-700' :
                      myth.category === 'acne' ? 'bg-red-100 text-red-700' :
                      myth.category === 'aging' ? 'bg-orange-100 text-orange-700' :
                      myth.category === 'sun-protection' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {categories.find(cat => cat.id === myth.category)?.name}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {filteredMyths.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Search className="w-12 h-12 mx-auto" />
                </div>
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No myths found</h3>
                <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="section-padding bg-brand-teal text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold mb-6">
            Stay Informed with Evidence-Based Skincare
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Don't fall for skincare myths. Get reliable, science-backed information 
            to make informed decisions about your skin health.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-brand-teal px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Explore Ingredients
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-brand-teal transition-colors">
              Read Our Guides
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkincareMythsPage;
