import React, { useState } from 'react';
import { Shield, AlertTriangle, CheckCircle, XCircle, Info, Search, Filter } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const IngredientSafetyPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const safetyCategories = [
    { id: 'all', name: 'All Interactions' },
    { id: 'safe', name: 'Safe Combinations' },
    { id: 'caution', name: 'Use with Caution' },
    { id: 'avoid', name: 'Avoid Mixing' },
    { id: 'pregnancy', name: 'Pregnancy Safety' },
  ];

  const ingredientInteractions = [
    {
      id: 1,
      ingredient1: 'Vitamin C',
      ingredient2: 'Retinol',
      category: 'caution',
      safetyLevel: 'Caution',
      interaction: 'Can be used together but may cause irritation in sensitive individuals',
      recommendation: 'Use Vitamin C in morning, Retinol at night, or alternate days',
      scientificBasis: 'Both are potent actives that can increase skin sensitivity when combined',
      riskLevel: 'Medium',
      commonSideEffects: ['Redness', 'Peeling', 'Irritation'],
      expertTip: 'Start with lower concentrations and gradually increase tolerance'
    },
    {
      id: 2,
      ingredient1: 'Niacinamide',
      ingredient2: 'Hyaluronic Acid',
      category: 'safe',
      safetyLevel: 'Safe',
      interaction: 'Excellent combination that enhances each other\'s benefits',
      recommendation: 'Can be used together morning and evening',
      scientificBasis: 'Niacinamide strengthens barrier while HA provides hydration',
      riskLevel: 'None',
      commonSideEffects: [],
      expertTip: 'Apply HA first, then niacinamide for optimal absorption'
    },
    {
      id: 3,
      ingredient1: 'Benzoyl Peroxide',
      ingredient2: 'Retinol',
      category: 'avoid',
      safetyLevel: 'Avoid',
      interaction: 'Can cause severe irritation and reduce effectiveness of both ingredients',
      recommendation: 'Use at different times or alternate days',
      scientificBasis: 'BP can oxidize retinol, reducing its efficacy while increasing irritation',
      riskLevel: 'High',
      commonSideEffects: ['Severe dryness', 'Burning', 'Peeling', 'Redness'],
      expertTip: 'Use BP in morning, retinol at night, with at least 12 hours between applications'
    }
  ];

  const pregnancySafetyGuide = [
    {
      ingredient: 'Retinoids (Retinol, Tretinoin)',
      safetyStatus: 'Avoid',
      reason: 'Potential teratogenic effects',
      alternatives: ['Bakuchiol', 'Vitamin C', 'Peptides'],
      trimesterGuidance: 'Avoid throughout pregnancy and breastfeeding'
    },
    {
      ingredient: 'Salicylic Acid (BHA)',
      safetyStatus: 'Limited Use',
      reason: 'High doses may be problematic',
      alternatives: ['Lactic Acid', 'Mandelic Acid', 'Azelaic Acid'],
      trimesterGuidance: 'Low concentrations in cleansers generally considered safe'
    },
    {
      ingredient: 'Hyaluronic Acid',
      safetyStatus: 'Safe',
      reason: 'No known risks during pregnancy',
      alternatives: [],
      trimesterGuidance: 'Safe throughout pregnancy and breastfeeding'
    }
  ];

  const filteredInteractions = ingredientInteractions.filter(interaction => {
    const matchesSearch = 
      interaction.ingredient1.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interaction.ingredient2.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interaction.interaction.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || interaction.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const getSafetyIcon = (level: string) => {
    switch (level) {
      case 'Safe':
        return <CheckCircle className="w-6 h-6 text-green-600" />;
      case 'Caution':
        return <AlertTriangle className="w-6 h-6 text-yellow-600" />;
      case 'Avoid':
        return <XCircle className="w-6 h-6 text-red-600" />;
      default:
        return <Info className="w-6 h-6 text-gray-600" />;
    }
  };

  const getSafetyColor = (level: string) => {
    switch (level) {
      case 'Safe':
        return 'bg-green-50 border-green-200';
      case 'Caution':
        return 'bg-yellow-50 border-yellow-200';
      case 'Avoid':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Ingredient Safety & Interactions Guide - Skincare Compass"
        description="Complete guide to skincare ingredient safety, interactions, and pregnancy considerations. Learn which ingredients to combine safely and which to avoid."
        keywords="skincare ingredient safety, ingredient interactions, pregnancy skincare, skincare combinations, ingredient compatibility"
        canonicalUrl="https://www.skincarecompass.com/ingredient-safety"
      />

      {/* Header */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
              <Shield className="w-8 h-8 text-brand-teal" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-6">
              Ingredient Safety & Interactions
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed mb-8">
              Your comprehensive guide to safe skincare ingredient combinations. Learn which ingredients 
              work well together, which to avoid mixing, and special considerations for pregnancy and sensitive skin.
            </p>
            
            {/* Safety Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-teal">200+</div>
                <div className="text-gray-600 text-sm">Interactions Analyzed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">150+</div>
                <div className="text-gray-600 text-sm">Safe Combinations</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-600">25+</div>
                <div className="text-gray-600 text-sm">Caution Pairings</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">15+</div>
                <div className="text-gray-600 text-sm">Avoid Combinations</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Safety Principles */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Key Safety Principles
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="text-xl font-bold text-brand-charcoal mb-3">Start Slowly</h3>
                <p className="text-gray-600">
                  Introduce new ingredients one at a time, allowing 2-4 weeks between additions to monitor reactions.
                </p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                  <AlertTriangle className="w-6 h-6 text-yellow-600" />
                </div>
                <h3 className="text-xl font-bold text-brand-charcoal mb-3">Patch Test</h3>
                <p className="text-gray-600">
                  Always patch test new products on your inner arm for 24-48 hours before applying to your face.
                </p>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Info className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-brand-charcoal mb-3">Know Your Limits</h3>
                <p className="text-gray-600">
                  Understand your skin's tolerance level and don't exceed recommended concentrations or frequencies.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search ingredient interactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                {safetyCategories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <p className="text-gray-600">
              Showing {filteredInteractions.length} interactions
            </p>
          </div>
        </div>
      </div>

      {/* Interactions List */}
      <div className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {filteredInteractions.length > 0 ? (
              <div className="space-y-6">
                {filteredInteractions.map((interaction) => (
                  <div
                    key={interaction.id}
                    className={`rounded-lg border p-6 ${getSafetyColor(interaction.safetyLevel)}`}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        {getSafetyIcon(interaction.safetyLevel)}
                        <div>
                          <h3 className="text-xl font-bold text-brand-charcoal">
                            {interaction.ingredient1} + {interaction.ingredient2}
                          </h3>
                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                            interaction.safetyLevel === 'Safe' ? 'bg-green-100 text-green-800' :
                            interaction.safetyLevel === 'Caution' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {interaction.safetyLevel}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-brand-charcoal mb-2">Interaction:</h4>
                        <p className="text-gray-600">{interaction.interaction}</p>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-brand-charcoal mb-2">Recommendation:</h4>
                        <p className="text-gray-600">{interaction.recommendation}</p>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-brand-charcoal mb-2">Scientific Basis:</h4>
                        <p className="text-gray-600">{interaction.scientificBasis}</p>
                      </div>
                      
                      {interaction.commonSideEffects.length > 0 && (
                        <div>
                          <h4 className="font-semibold text-brand-charcoal mb-2">Potential Side Effects:</h4>
                          <div className="flex flex-wrap gap-2">
                            {interaction.commonSideEffects.map((effect, index) => (
                              <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                                {effect}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-semibold text-blue-800 mb-2">Expert Tip:</h4>
                        <p className="text-blue-700">{interaction.expertTip}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Shield className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No interactions found</h3>
                <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IngredientSafetyPage;
