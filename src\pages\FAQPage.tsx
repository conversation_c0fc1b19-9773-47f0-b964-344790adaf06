import React, { useState } from 'react';
import { Search, ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const FAQPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [openItems, setOpenItems] = useState<number[]>([]);

  const categories = [
    { id: 'all', name: 'All Questions' },
    { id: 'basics', name: 'Skincare Basics' },
    { id: 'ingredients', name: 'Ingredients' },
    { id: 'routines', name: 'Routines' },
    { id: 'concerns', name: 'Skin Concerns' },
    { id: 'products', name: 'Product Selection' },
    { id: 'safety', name: 'Safety & Side Effects' },
  ];

  const faqs = [
    // Skincare Basics
    {
      id: 1,
      category: 'basics',
      question: 'What is the basic skincare routine I should follow?',
      answer: 'A basic skincare routine consists of three essential steps: cleanse, moisturize, and protect with sunscreen. Start with a gentle cleanser suitable for your skin type, follow with a moisturizer, and always use broad-spectrum SPF 30+ sunscreen during the day. This foundation can be built upon as you learn more about your skin\'s needs. For evening, you can add a treatment serum between cleansing and moisturizing.',
    },
    {
      id: 2,
      category: 'basics',
      question: 'How do I determine my skin type?',
      answer: 'Your skin type is determined by how much oil your skin produces. Wash your face with a gentle cleanser, pat dry, and wait 30 minutes without applying any products. If your skin feels tight and looks flaky, you likely have dry skin. If it\'s shiny all over, you have oily skin. If only your T-zone (forehead, nose, chin) is oily while cheeks are normal or dry, you have combination skin. If your skin feels comfortable without being oily or dry, you have normal skin. Remember, skin type can change with age, hormones, and seasons.',
    },
    {
      id: 3,
      category: 'basics',
      question: 'When should I start using anti-aging products?',
      answer: 'Prevention is key in anti-aging. You can start using gentle anti-aging ingredients like vitamin C and retinol in your mid-20s. However, the most important anti-aging step is consistent sunscreen use, which should start as early as possible. Focus on prevention rather than correction, and introduce active ingredients gradually to avoid irritation. A good antioxidant serum and retinol product, along with daily SPF, form the foundation of any anti-aging routine.',
    },
    {
      id: 4,
      category: 'basics',
      question: 'How often should I wash my face?',
      answer: 'Most people should wash their face twice daily - once in the morning and once at night. Morning cleansing removes overnight buildup of oils and prepares skin for products. Evening cleansing removes makeup, sunscreen, pollution, and daily buildup. However, if you have very dry or sensitive skin, you might benefit from just rinsing with water in the morning and only using cleanser at night. Over-cleansing can strip the skin barrier and cause irritation.',
    },
    {
      id: 5,
      category: 'basics',
      question: 'What\'s the correct order to apply skincare products?',
      answer: 'Apply products from thinnest to thickest consistency: cleanser, toner/essence, serums (vitamin C, niacinamide, hyaluronic acid), treatments (retinol, acids), moisturizer, and sunscreen (AM only). Wait 1-2 minutes between each step for proper absorption. In the morning: cleanser → vitamin C serum → moisturizer → sunscreen. In the evening: cleanser → treatment serum → moisturizer. This order ensures maximum absorption and effectiveness.',
    },

    // Ingredients
    {
      id: 6,
      category: 'ingredients',
      question: 'Can I use vitamin C and retinol together?',
      answer: 'It\'s generally recommended to use vitamin C in the morning and retinol at night rather than together. Vitamin C works best in an acidic environment and provides antioxidant protection during the day, while retinol is photosensitive and works optimally at night. Using them at different times maximizes their effectiveness and minimizes potential irritation. If you want to use both in the same routine, apply vitamin C first, wait 20-30 minutes, then apply retinol.',
    },
    {
      id: 7,
      category: 'ingredients',
      question: 'How long does it take to see results from skincare products?',
      answer: 'Results vary by ingredient and skin concern. Hydrating products may show immediate effects, while active ingredients like retinol and vitamin C typically require 4-6 weeks for noticeable improvements. For acne treatments, initial results may appear in 2-4 weeks, but full benefits often take 8-12 weeks. Anti-aging results can take 3-6 months to become apparent. Consistency is key for achieving and maintaining results. Take progress photos to track subtle changes.',
    },
    {
      id: 8,
      category: 'ingredients',
      question: 'What\'s the difference between AHA and BHA?',
      answer: 'AHAs (Alpha Hydroxy Acids) like glycolic and lactic acid are water-soluble and work on the skin\'s surface to exfoliate dead skin cells, improve texture, and reduce fine lines. They\'re ideal for dry skin, sun damage, and anti-aging. BHAs (Beta Hydroxy Acids) like salicylic acid are oil-soluble and can penetrate into pores, making them ideal for acne-prone and oily skin. BHAs also have anti-inflammatory properties. Start with lower concentrations (5-10% AHA, 0.5-2% BHA) and use 2-3 times per week initially.',
    },
    {
      id: 9,
      category: 'ingredients',
      question: 'What concentration of retinol should I start with?',
      answer: 'Beginners should start with 0.25% or 0.5% retinol, used once or twice per week initially. Gradually increase frequency as your skin builds tolerance, then consider increasing concentration. Most people find 0.5-1% retinol effective for anti-aging benefits. Higher concentrations (1-2%) are for experienced users. Always introduce retinol slowly, use at night only, and increase sunscreen diligence. Expect some initial dryness and peeling - this is normal and usually subsides within 2-4 weeks.',
    },
    {
      id: 10,
      category: 'ingredients',
      question: 'Can I use niacinamide with other active ingredients?',
      answer: 'Niacinamide is one of the most compatible active ingredients and can be used with most other actives. It pairs well with hyaluronic acid, peptides, and even vitamin C (despite old myths about incompatibility). It can help buffer the irritation from retinoids and acids. However, some people may experience flushing when combining niacinamide with vitamin C - if this happens, use them at different times. Niacinamide is gentle enough for twice-daily use and suitable for all skin types.',
    },

    // Skin Concerns
    {
      id: 11,
      category: 'concerns',
      question: 'How can I get rid of acne scars?',
      answer: 'Acne scars require different treatments depending on their type. For post-inflammatory hyperpigmentation (dark spots), use ingredients like vitamin C, niacinamide, kojic acid, and gentle exfoliants like lactic acid. For textural scars (ice pick, boxcar, rolling), topical retinoids can help, but professional treatments like microneedling, chemical peels, or laser therapy are often necessary. Always use broad-spectrum sunscreen to prevent darkening of existing marks. Be patient - scar improvement can take 6-12 months or longer.',
    },
    {
      id: 12,
      category: 'concerns',
      question: 'Why is my skin getting worse after starting a new routine?',
      answer: 'This could be "purging" if you\'ve introduced active ingredients like retinoids, AHAs, or BHAs, which accelerate cell turnover and bring underlying congestion to the surface. Purging typically occurs in areas where you normally break out and should improve within 4-6 weeks. However, if you\'re breaking out in new areas, experiencing severe irritation, or symptoms persist beyond 6 weeks, you may be having an adverse reaction and should discontinue the product. Introduce new actives one at a time to identify potential culprits.',
    },
    {
      id: 13,
      category: 'concerns',
      question: 'How do I deal with sensitive skin?',
      answer: 'For sensitive skin, focus on gentle, fragrance-free products with minimal ingredients. Introduce new products one at a time and patch test first. Look for soothing ingredients like ceramides, niacinamide, centella asiatica, and colloidal oatmeal. Avoid harsh scrubs, high concentrations of actives, alcohol, fragrances, essential oils, and sulfates. Build your routine slowly and prioritize barrier repair. Use lukewarm water, pat skin dry gently, and apply moisturizer while skin is still damp. Consider seeing a dermatologist if sensitivity is severe.',
    },
    {
      id: 14,
      category: 'concerns',
      question: 'What causes hormonal acne and how do I treat it?',
      answer: 'Hormonal acne is caused by fluctuations in hormones (particularly androgens) that increase oil production and inflammation. It typically appears along the jawline, chin, and lower cheeks, and often worsens around menstruation. Treatment includes gentle cleansing, salicylic acid or benzoyl peroxide for active breakouts, niacinamide for oil control, and retinoids for long-term improvement. Lifestyle factors like stress management, adequate sleep, and a balanced diet can help. For severe cases, consult a dermatologist about hormonal treatments or prescription medications.',
    },
    {
      id: 15,
      category: 'concerns',
      question: 'How do I minimize large pores?',
      answer: 'While you can\'t permanently shrink pores (their size is largely genetic), you can minimize their appearance. Keep pores clean with gentle exfoliation using BHAs like salicylic acid, which can penetrate and clear out buildup. Retinoids help improve skin texture and reduce pore appearance over time. Niacinamide can help regulate oil production. Always use sunscreen to prevent collagen breakdown that can make pores appear larger. Professional treatments like chemical peels or microneedling can provide more dramatic results.',
    },

    // Product Selection
    {
      id: 16,
      category: 'products',
      question: 'How do I choose the right moisturizer?',
      answer: 'Choose your moisturizer based on your skin type and concerns. Dry skin needs rich, occlusive ingredients like ceramides, hyaluronic acid, and shea butter. Oily skin benefits from lightweight, non-comedogenic formulas with ingredients like niacinamide and hyaluronic acid. Sensitive skin should look for fragrance-free, minimal ingredient lists with soothing ingredients. Consider your climate and season - you may need different moisturizers for summer and winter. Look for moisturizers with SPF for daytime use, but use a separate sunscreen for better protection.',
    },
    {
      id: 17,
      category: 'products',
      question: 'Are expensive skincare products worth it?',
      answer: 'Price doesn\'t always correlate with effectiveness. Many affordable products contain the same active ingredients as expensive ones at similar concentrations. Focus on the ingredient list, concentration, and formulation quality rather than price. However, some expensive products may offer superior packaging (airless pumps, dark bottles), stability, unique ingredient combinations, or higher concentrations of actives. Research ingredients, read reviews, and consider your budget. Sometimes a $10 product works better than a $100 one - it\'s about finding what works for your skin.',
    },
    {
      id: 18,
      category: 'products',
      question: 'Should I use different products for day and night?',
      answer: 'Yes, your skin has different needs during the day versus night. Daytime routines should focus on protection with antioxidants like vitamin C and broad-spectrum sunscreen. Nighttime is ideal for repair and renewal with ingredients like retinoids, AHAs, and rich moisturizers. Your skin\'s natural repair processes are most active at night, making it the perfect time for treatment products. Some ingredients like retinol are photosensitive and should only be used at night. You can use the same cleanser and moisturizer day and night if they work well for you.',
    },
    {
      id: 19,
      category: 'products',
      question: 'How do I know if a product is working?',
      answer: 'Track your skin\'s progress with photos taken in consistent lighting and angles. Keep a skincare journal noting products used and skin condition. Positive signs include improved texture, reduced breakouts, more even skin tone, and better hydration. However, be patient - most products need 4-6 weeks to show results, and some benefits (like anti-aging) take months. If you experience persistent irritation, increased breakouts in new areas, or worsening of your target concern after 6-8 weeks, the product may not be right for you.',
    },
    {
      id: 20,
      category: 'products',
      question: 'What ingredients should I avoid?',
      answer: 'Ingredients to generally avoid or use with caution include: harsh sulfates (SLS) which can strip the skin, denatured alcohol which can be drying, fragrances and essential oils (especially if you have sensitive skin), and physical scrubs with large, rough particles. Be cautious with high concentrations of actives if you\'re a beginner. However, ingredient tolerance is individual - what irritates one person may work well for another. Always patch test new products and introduce them gradually. Focus on avoiding ingredients you know cause reactions for your specific skin.',
    },

    // Routines
    {
      id: 21,
      category: 'routines',
      question: 'How do I build a skincare routine from scratch?',
      answer: 'Start with the basics: gentle cleanser, moisturizer, and broad-spectrum sunscreen. Use these for 2-4 weeks to establish a baseline. Then add one new product every 2-4 weeks, starting with the most important for your concerns (vitamin C for brightening, retinol for anti-aging, salicylic acid for acne). Always patch test new products. Build slowly - a simple, consistent routine is better than a complex one you can\'t maintain. Focus on addressing your primary skin concern first.',
    },
    {
      id: 22,
      category: 'routines',
      question: 'How many products should I use in my routine?',
      answer: 'There\'s no magic number - it depends on your skin\'s needs and your lifestyle. A basic routine might have 3-4 products (cleanser, treatment serum, moisturizer, sunscreen), while a comprehensive routine might have 6-8 products. More isn\'t always better - using too many products can cause irritation and make it hard to identify what\'s working. Focus on quality over quantity and ensure each product serves a specific purpose in addressing your skin concerns.',
    },
    {
      id: 23,
      category: 'routines',
      question: 'Should I change my routine seasonally?',
      answer: 'Yes, seasonal adjustments can be beneficial. In winter, you might need richer moisturizers, gentler cleansers, and more hydrating products due to lower humidity and indoor heating. In summer, lighter formulations, oil-control products, and higher SPF might be necessary. However, keep your core routine consistent and make gradual adjustments. Some people need minimal changes, while others require significant seasonal modifications. Pay attention to how your skin responds to environmental changes.',
    },
    {
      id: 24,
      category: 'routines',
      question: 'When should I see a dermatologist?',
      answer: 'See a dermatologist for persistent acne that doesn\'t respond to over-the-counter treatments, sudden changes in moles or skin growths, severe skin reactions, chronic conditions like eczema or rosacea, or if you want professional guidance on anti-aging treatments. Also consult a dermatologist before starting prescription treatments or if you have concerns about skin cancer risk. Many dermatologists also offer cosmetic consultations for professional treatments like chemical peels or laser therapy.',
    },

    // Safety & Side Effects
    {
      id: 25,
      category: 'safety',
      question: 'What\'s the difference between chemical and mineral sunscreen?',
      answer: 'Chemical sunscreens (avobenzone, octinoxate, oxybenzone) absorb UV rays and convert them to heat. They\'re typically lightweight and blend easily but may cause irritation in sensitive individuals. Mineral sunscreens (zinc oxide, titanium dioxide) sit on the skin surface and physically block UV rays. They\'re gentler and provide immediate protection but can leave a white cast and feel heavier. Both are effective when used properly. Choose based on your skin type, preferences, and any sensitivities.',
    },
    {
      id: 26,
      category: 'safety',
      question: 'How much sunscreen should I apply?',
      answer: 'Apply 1/4 teaspoon (about 1.25ml) of sunscreen to your face and neck - this is more than most people use. For your body, use about 1 ounce (30ml) for full coverage. The key is generous, even application. Most people apply only 25-50% of the recommended amount, which significantly reduces protection. Reapply every 2 hours, or immediately after swimming, sweating, or toweling off. Don\'t forget often-missed areas like ears, eyelids, lips, and the back of your neck.',
    },
    {
      id: 27,
      category: 'safety',
      question: 'Do I need sunscreen indoors or on cloudy days?',
      answer: 'Yes, you should wear sunscreen indoors if you\'re near windows, as UVA rays can penetrate glass and cause aging and pigmentation. On cloudy days, up to 80% of UV rays can still reach your skin. UVA rays (which cause aging) are present year-round and can penetrate clouds and glass. If you\'re indoors all day away from windows, you can skip sunscreen, but for most people, daily application is the best habit for consistent protection.',
    },
    {
      id: 28,
      category: 'safety',
      question: 'Can I use makeup with SPF instead of sunscreen?',
      answer: 'Makeup with SPF can provide some protection, but it\'s rarely sufficient as your sole sun protection. Most people don\'t apply makeup thickly enough to achieve the stated SPF. Use a dedicated broad-spectrum sunscreen as your base, then apply makeup with SPF on top for additional protection. Powder sunscreens can be good for touch-ups throughout the day. For reliable protection, always use a proper sunscreen as your primary defense against UV damage.',
    },
    {
      id: 29,
      category: 'safety',
      question: 'What should I do if I have an allergic reaction to a skincare product?',
      answer: 'If you experience an allergic reaction (redness, swelling, itching, burning, or rash), immediately stop using the product and rinse your skin with cool water. Apply a gentle, fragrance-free moisturizer or aloe vera gel to soothe the skin. If symptoms are severe or don\'t improve within 24-48 hours, consult a healthcare professional or dermatologist. Keep the product and ingredient list to help identify the specific allergen. In the future, always patch test new products before full application.',
    },
    {
      id: 30,
      category: 'safety',
      question: 'Is it safe to use expired skincare products?',
      answer: 'It\'s generally not recommended to use expired skincare products. Over time, active ingredients lose potency, preservatives break down (increasing contamination risk), and the product\'s pH may change, potentially causing irritation. Some signs of expired products include changes in color, texture, smell, or separation. Vitamin C serums are particularly prone to oxidation and should be discarded if they turn brown or orange. When in doubt, replace the product - your skin\'s health is worth more than saving a few dollars.',
    },
  ];

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Frequently Asked Questions - Skincare Compass Help Center"
        description="Find answers to common questions about skincare ingredients, routines, and using Skincare Compass tools. Get expert help with your skincare journey."
        keywords="skincare FAQ, skincare questions, ingredient help, routine help, skincare compass help, skincare guidance"
        canonicalUrl="https://www.skincarecompass.com/faq"
      />
      {/* Header */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
              <HelpCircle className="w-8 h-8 text-brand-teal" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Find answers to common questions about Skincare Compass, our tools, 
              and how to make the most of our platform.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search FAQs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <p className="text-gray-600">
              Showing {filteredFAQs.length} questions
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Items */}
      <div className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.length > 0 ? (
              <div className="space-y-4">
                {filteredFAQs.map((faq, index) => (
                  <div
                    key={faq.id}
                    className="card overflow-hidden animate-slide-up"
                    style={{ animationDelay: `${index * 0.05}s` }}
                  >
                    <button
                      onClick={() => toggleItem(faq.id)}
                      className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                    >
                      <h3 className="text-lg font-semibold text-brand-charcoal pr-4">
                        {faq.question}
                      </h3>
                      {openItems.includes(faq.id) ? (
                        <ChevronUp className="w-5 h-5 text-gray-400 flex-shrink-0" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                      )}
                    </button>
                    
                    {openItems.includes(faq.id) && (
                      <div className="px-6 pb-4 border-t border-gray-100">
                        <p className="text-gray-600 leading-relaxed pt-4">
                          {faq.answer}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <HelpCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No questions found</h3>
                <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
                Still Have Questions?
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Can't find what you're looking for? Our community forum is a great place to ask questions
                and get answers from other skincare enthusiasts.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/about" className="btn-primary">
                  About Us
                </a>
                <a href="/blog" className="btn-secondary">
                  Read Our Blog
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQPage;