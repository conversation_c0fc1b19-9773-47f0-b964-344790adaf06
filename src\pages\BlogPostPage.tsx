import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useLocation } from 'react-router-dom';
import { ArrowLeft, Tag, Share2, BarChart3, TrendingUp, Lightbulb, CheckCircle, AlertTriangle, BookOpen, ArrowRight } from 'lucide-react';
import { blogPosts } from '../data/blogPosts';
import MetaTags from '../components/SEO/MetaTags';
import TableOfContents from '../components/blog/TableOfContents';

// Component to render blog content with proper formatting
const BlogContent: React.FC<{ content: string }> = ({ content }) => {
  const formatContent = (content: string) => {
    const formatted = content;

    // Split content into sections
    const sections = formatted.split('\n\n');
    const processedSections = sections.map(section => {
      const trimmed = section.trim();

      if (!trimmed) return '';

      // Handle images
      if (trimmed.startsWith('![') && trimmed.includes('](')) {
        return `<div class="mb-8">${trimmed}</div>`;
      }

      // Handle italic text (image captions)
      if (trimmed.startsWith('*') && trimmed.endsWith('*') && !trimmed.includes('**')) {
        return `<p class="text-gray-600 text-sm italic text-center mb-8">${trimmed.slice(1, -1)}</p>`;
      }

      // Handle bold headings
      if (trimmed.startsWith('**') && trimmed.endsWith('**')) {
        const headingText = trimmed.slice(2, -2);

        // Special styling for "In This Article"
        if (headingText === 'In This Article') {
          return `<h3 class="text-lg font-semibold text-brand-charcoal mt-8 mb-4">${headingText}</h3>`;
        }

        const anchorId = headingText.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        return `<h3 id="${anchorId}" class="text-xl font-semibold text-brand-charcoal mt-10 mb-6">${headingText}</h3>`;
      }

      // Handle bullet point lists
      if (trimmed.includes('\n•') || trimmed.startsWith('•')) {
        const listItems = trimmed.split('\n').filter(line => line.trim().startsWith('•'));
        const listHTML = listItems.map(item => {
          const text = item.replace(/^•\s*/, '').replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
          return `<li>${text}</li>`;
        }).join('');
        return `<ul class="mb-6 space-y-2 pl-4">${listHTML}</ul>`;
      }

      // Handle regular paragraphs
      if (!trimmed.includes('<') && !trimmed.startsWith('**')) {
        const formatted = trimmed.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        return `<p class="mb-4 text-gray-700 leading-relaxed">${formatted}</p>`;
      }

      return trimmed;
    });

    return processedSections.join('');
  };

  return (
    <div
      className="blog-content"
      dangerouslySetInnerHTML={{ __html: formatContent(content) }}
    />
  );
};

const BlogPostPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const location = useLocation();
  
  // Scroll to top when navigating to a new page
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);
  
  // Get blog post data
  const post = slug ? blogPosts[slug] : null;

  if (!post) {
    return (
      <div className="pt-16 min-h-screen bg-brand-off-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-brand-charcoal mb-4">Article Not Found</h1>
          <Link to="/blog" className="btn-primary">
            Browse All Articles
          </Link>
        </div>
      </div>
    );
  }

  // SEO Data
  const seoTitle = `${post.title} | Skincare Compass Blog`;
  const seoDescription = post.excerpt;
  const canonicalUrl = `https://www.skincarecompass.com/blog/${slug}`;

  // Structured Data for Article
  const articleStructuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": post.image,
    "author": {
      "@type": "Organization",
      "name": "Skincare Compass"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Skincare Compass",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.skincarecompass.com/logo.png"
      }
    },
    "datePublished": post.publishDate,
    "dateModified": post.lastUpdated,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    },
    "wordCount": post.content.length,
    "timeRequired": post.readTime,
    "keywords": post.tags.join(', ')
  };

  // Get related posts based on category and tags
  const getRelatedPosts = () => {
    return Object.values(blogPosts)
      .filter(p => 
        p.slug !== post.slug && 
        (p.category === post.category || 
         p.tags.some(tag => post.tags.includes(tag)))
      )
      .sort((a, b) => {
        // Count matching tags
        const aMatchCount = a.tags.filter(tag => post.tags.includes(tag)).length;
        const bMatchCount = b.tags.filter(tag => post.tags.includes(tag)).length;
        
        // Sort by match count, then by popularity
        if (bMatchCount !== aMatchCount) {
          return bMatchCount - aMatchCount;
        }
        return b.isPopular ? 1 : -1;
      })
      .slice(0, 3);
  };

  const relatedPosts = getRelatedPosts();

  // Generate infographics based on article content
  const renderInfographics = () => {
    switch (slug) {
      case 'retinol-vs-bakuchiol-complete-comparison':
        return (
          <div className="my-12 space-y-8">
            {/* Comparison Chart */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
                  <BarChart3 className="w-8 h-8 text-brand-teal" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Retinol vs. Bakuchiol: Side-by-Side Comparison
                </h3>
                <p className="text-gray-600">
                  Visual comparison of key differences between these anti-aging ingredients
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="text-center">
                    <h4 className="text-xl font-bold text-purple-600 mb-4">Retinol</h4>
                    <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl">🧪</span>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-purple-50 rounded-lg p-4">
                      <h5 className="font-semibold text-purple-800 mb-2">Efficacy</h5>
                      <div className="w-full bg-purple-200 rounded-full h-3">
                        <div className="bg-purple-600 h-3 rounded-full" style={{ width: '95%' }}></div>
                      </div>
                      <span className="text-sm text-purple-700">95% - Gold Standard</span>
                    </div>
                    
                    <div className="bg-red-50 rounded-lg p-4">
                      <h5 className="font-semibold text-red-800 mb-2">Irritation Risk</h5>
                      <div className="w-full bg-red-200 rounded-full h-3">
                        <div className="bg-red-600 h-3 rounded-full" style={{ width: '75%' }}></div>
                      </div>
                      <span className="text-sm text-red-700">75% - High Initial Risk</span>
                    </div>
                    
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <h5 className="font-semibold text-yellow-800 mb-2">Pregnancy Safety</h5>
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="w-5 h-5 text-yellow-600" />
                        <span className="text-yellow-700">Not Recommended</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="text-center">
                    <h4 className="text-xl font-bold text-green-600 mb-4">Bakuchiol</h4>
                    <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl">🌿</span>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-green-50 rounded-lg p-4">
                      <h5 className="font-semibold text-green-800 mb-2">Efficacy</h5>
                      <div className="w-full bg-green-200 rounded-full h-3">
                        <div className="bg-green-600 h-3 rounded-full" style={{ width: '70%' }}></div>
                      </div>
                      <span className="text-sm text-green-700">70% - Gentle Alternative</span>
                    </div>
                    
                    <div className="bg-green-50 rounded-lg p-4">
                      <h5 className="font-semibold text-green-800 mb-2">Irritation Risk</h5>
                      <div className="w-full bg-green-200 rounded-full h-3">
                        <div className="bg-green-600 h-3 rounded-full" style={{ width: '15%' }}></div>
                      </div>
                      <span className="text-sm text-green-700">15% - Very Low Risk</span>
                    </div>
                    
                    <div className="bg-green-50 rounded-lg p-4">
                      <h5 className="font-semibold text-green-800 mb-2">Pregnancy Safety</h5>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <span className="text-green-700">Safe to Use</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Decision Tree */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <TrendingUp className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Which Should You Choose?
                </h3>
                <p className="text-gray-600">
                  Decision flowchart to help you pick the right ingredient
                </p>
              </div>

              <div className="max-w-3xl mx-auto">
                <div className="space-y-6">
                  <div className="bg-blue-50 rounded-lg p-6 text-center">
                    <h4 className="font-bold text-blue-800 mb-4">Are you pregnant or breastfeeding?</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="bg-green-100 rounded-lg p-4">
                        <span className="font-semibold text-green-800">YES → Choose Bakuchiol</span>
                        <p className="text-sm text-green-700 mt-2">Safe during pregnancy and breastfeeding</p>
                      </div>
                      <div className="bg-gray-100 rounded-lg p-4">
                        <span className="font-semibold text-gray-800">NO → Continue below</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-50 rounded-lg p-6 text-center">
                    <h4 className="font-bold text-purple-800 mb-4">Do you have sensitive skin?</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="bg-green-100 rounded-lg p-4">
                        <span className="font-semibold text-green-800">YES → Start with Bakuchiol</span>
                        <p className="text-sm text-green-700 mt-2">Gentler introduction to anti-aging</p>
                      </div>
                      <div className="bg-gray-100 rounded-lg p-4">
                        <span className="font-semibold text-gray-800">NO → Continue below</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 rounded-lg p-6 text-center">
                    <h4 className="font-bold text-orange-800 mb-4">Want maximum anti-aging results?</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="bg-purple-100 rounded-lg p-4">
                        <span className="font-semibold text-purple-800">YES → Choose Retinol</span>
                        <p className="text-sm text-purple-700 mt-2">Gold standard with proven results</p>
                      </div>
                      <div className="bg-green-100 rounded-lg p-4">
                        <span className="font-semibold text-green-800">NO → Bakuchiol is perfect</span>
                        <p className="text-sm text-green-700 mt-2">Gentle, effective alternative</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'niacinamide-complete-guide':
        return (
          <div className="my-12 space-y-8">
            {/* Benefits Breakdown */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
                  <BarChart3 className="w-8 h-8 text-brand-teal" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Niacinamide Benefits by Skin Type
                </h3>
                <p className="text-gray-600">
                  How niacinamide helps different skin concerns
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">💧</span>
                  </div>
                  <h4 className="font-bold text-blue-800 mb-2">Oily Skin</h4>
                  <div className="space-y-2 text-sm">
                    <div className="bg-blue-50 rounded-lg p-2">
                      <span className="font-medium">Oil Control:</span> 30% reduction
                    </div>
                    <div className="bg-blue-50 rounded-lg p-2">
                      <span className="font-medium">Pore Appearance:</span> 23% improvement
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🌿</span>
                  </div>
                  <h4 className="font-bold text-green-800 mb-2">Sensitive Skin</h4>
                  <div className="space-y-2 text-sm">
                    <div className="bg-green-50 rounded-lg p-2">
                      <span className="font-medium">Redness:</span> 42% reduction
                    </div>
                    <div className="bg-green-50 rounded-lg p-2">
                      <span className="font-medium">Irritation:</span> Minimal risk
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">✨</span>
                  </div>
                  <h4 className="font-bold text-purple-800 mb-2">Aging Skin</h4>
                  <div className="space-y-2 text-sm">
                    <div className="bg-purple-50 rounded-lg p-2">
                      <span className="font-medium">Fine Lines:</span> 82% improvement
                    </div>
                    <div className="bg-purple-50 rounded-lg p-2">
                      <span className="font-medium">Elasticity:</span> 45% increase
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🏜️</span>
                  </div>
                  <h4 className="font-bold text-yellow-800 mb-2">Dry Skin</h4>
                  <div className="space-y-2 text-sm">
                    <div className="bg-yellow-50 rounded-lg p-2">
                      <span className="font-medium">Barrier Function:</span> 34% improvement
                    </div>
                    <div className="bg-yellow-50 rounded-lg p-2">
                      <span className="font-medium">Hydration:</span> 68% increase
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Concentration Guide */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                  <Lightbulb className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Niacinamide Concentration Guide
                </h3>
                <p className="text-gray-600">
                  Finding the right strength for your skin
                </p>
              </div>

              <div className="space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-bold text-green-800">2-5% Niacinamide</h4>
                    <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Beginner Friendly</span>
                  </div>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-green-700">Best For:</span>
                      <p className="text-green-600">Sensitive skin, first-time users</p>
                    </div>
                    <div>
                      <span className="font-medium text-green-700">Benefits:</span>
                      <p className="text-green-600">Gentle introduction, minimal irritation</p>
                    </div>
                    <div>
                      <span className="font-medium text-green-700">Timeline:</span>
                      <p className="text-green-600">Results in 6-8 weeks</p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-bold text-blue-800">5-10% Niacinamide</h4>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Sweet Spot</span>
                  </div>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-blue-700">Best For:</span>
                      <p className="text-blue-600">Most skin types, optimal efficacy</p>
                    </div>
                    <div>
                      <span className="font-medium text-blue-700">Benefits:</span>
                      <p className="text-blue-600">Maximum benefits, well-tolerated</p>
                    </div>
                    <div>
                      <span className="font-medium text-blue-700">Timeline:</span>
                      <p className="text-blue-600">Results in 4-6 weeks</p>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-bold text-yellow-800">10%+ Niacinamide</h4>
                    <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Use with Caution</span>
                  </div>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-yellow-700">Best For:</span>
                      <p className="text-yellow-600">Experienced users only</p>
                    </div>
                    <div>
                      <span className="font-medium text-yellow-700">Benefits:</span>
                      <p className="text-yellow-600">Not necessarily better than 5-10%</p>
                    </div>
                    <div>
                      <span className="font-medium text-yellow-700">Risk:</span>
                      <p className="text-yellow-600">May cause irritation</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'morning-routine-sensitive-skin':
        return (
          <div className="my-12 space-y-8">
            {/* Routine Flowchart */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
                  <TrendingUp className="w-8 h-8 text-brand-teal" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Sensitive Skin Morning Routine Flowchart
                </h3>
                <p className="text-gray-600">
                  Step-by-step visual guide for gentle morning care
                </p>
              </div>

              <div className="max-w-2xl mx-auto space-y-4">
                <div className="bg-blue-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-blue-800 mb-2">Step 1: Gentle Cleansing (Optional)</h4>
                  <p className="text-sm text-blue-700">💧 Lukewarm water only OR gentle cream cleanser</p>
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-300"></div>
                </div>
                
                <div className="bg-green-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-green-800 mb-2">Step 2: Hydrating Serum</h4>
                  <p className="text-sm text-green-700">🧴 Hyaluronic acid or ceramide serum</p>
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-300"></div>
                </div>
                
                <div className="bg-purple-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-purple-800 mb-2">Step 3: Gentle Active (Optional)</h4>
                  <p className="text-sm text-purple-700">✨ 5% Niacinamide OR 10% Azelaic Acid</p>
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-300"></div>
                </div>
                
                <div className="bg-yellow-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-yellow-800 mb-2">Step 4: Rich Moisturizer</h4>
                  <p className="text-sm text-yellow-700">🧴 Ceramides, fatty acids, fragrance-free</p>
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-300"></div>
                </div>
                
                <div className="bg-orange-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-orange-800 mb-2">Step 5: Mineral Sunscreen</h4>
                  <p className="text-sm text-orange-700">☀️ Zinc oxide/Titanium dioxide SPF 30+</p>
                </div>
              </div>
            </div>

            {/* Ingredients to Avoid */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Ingredients to Avoid for Sensitive Skin
                </h3>
                <p className="text-gray-600">
                  Common irritants that can trigger sensitivity
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-bold text-red-800 mb-3">Fragrances & Essential Oils</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Parfum/Fragrance</li>
                    <li>• Lavender oil</li>
                    <li>• Tea tree oil</li>
                    <li>• Citrus oils</li>
                  </ul>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-bold text-red-800 mb-3">Harsh Alcohols</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Denatured alcohol</li>
                    <li>• SD alcohol</li>
                    <li>• Isopropyl alcohol</li>
                    <li>• Ethanol</li>
                  </ul>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-bold text-red-800 mb-3">Strong Actives</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• High % AHA/BHA</li>
                    <li>• Retinoids (AM)</li>
                    <li>• Benzoyl peroxide</li>
                    <li>• High % Vitamin C</li>
                  </ul>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-bold text-red-800 mb-3">Sulfates</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• SLS</li>
                    <li>• SLES</li>
                    <li>• Ammonium lauryl sulfate</li>
                    <li>• Sodium C14-16 olefin sulfonate</li>
                  </ul>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-bold text-red-800 mb-3">Preservatives</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Formaldehyde releasers</li>
                    <li>• MIT/CMIT</li>
                    <li>• Benzalkonium chloride</li>
                    <li>• Quaternium-15</li>
                  </ul>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-bold text-red-800 mb-3">Other Irritants</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Menthol</li>
                    <li>• Camphor</li>
                    <li>• Dyes/Colorants</li>
                    <li>• Witch hazel</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      case 'vitamin-c-stability-explained':
        return (
          <div className="my-12 space-y-8">
            {/* Vitamin C Stability Chart */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4">
                  <BarChart3 className="w-8 h-8 text-orange-600" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Vitamin C Stability Comparison
                </h3>
                <p className="text-gray-600">
                  Different forms of vitamin C and their stability characteristics
                </p>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="p-3 text-left text-sm font-semibold text-brand-charcoal border border-gray-200">Form of Vitamin C</th>
                      <th className="p-3 text-center text-sm font-semibold text-brand-charcoal border border-gray-200">Stability</th>
                      <th className="p-3 text-center text-sm font-semibold text-brand-charcoal border border-gray-200">Efficacy</th>
                      <th className="p-3 text-left text-sm font-semibold text-brand-charcoal border border-gray-200">Best For</th>
                      <th className="p-3 text-left text-sm font-semibold text-brand-charcoal border border-gray-200">Storage Tips</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="p-3 border border-gray-200 font-medium text-brand-charcoal">L-Ascorbic Acid</td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-red-500 h-2 rounded-full" style={{ width: '20%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Poor</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-green-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Excellent</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Maximum results, if you can maintain stability</td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Refrigerate, use quickly, dark bottle</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="p-3 border border-gray-200 font-medium text-brand-charcoal">Magnesium Ascorbyl Phosphate (MAP)</td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-green-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Excellent</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '70%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Good</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Sensitive skin, beginners</td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Room temperature is fine</td>
                    </tr>
                    <tr>
                      <td className="p-3 border border-gray-200 font-medium text-brand-charcoal">Sodium Ascorbyl Phosphate (SAP)</td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Very Good</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Good</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Acne-prone skin</td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Room temperature is fine</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="p-3 border border-gray-200 font-medium text-brand-charcoal">Ascorbyl Glucoside</td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-green-500 h-2 rounded-full" style={{ width: '95%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Excellent</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-center">
                        <div className="flex items-center justify-center">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                          </div>
                          <span className="ml-2 text-xs">Moderate</span>
                        </div>
                      </td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Long-term use, sensitive skin</td>
                      <td className="p-3 border border-gray-200 text-sm text-gray-600">Room temperature is fine</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Oxidation Visual Guide */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full mb-4">
                  <Lightbulb className="w-8 h-8 text-yellow-600" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Vitamin C Oxidation Visual Guide
                </h3>
                <p className="text-gray-600">
                  How to tell if your vitamin C serum has gone bad
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-24 h-24 bg-yellow-50 rounded-full flex items-center justify-center mx-auto mb-4 border-4 border-yellow-100">
                    <div className="w-16 h-16 bg-yellow-100 rounded-full"></div>
                  </div>
                  <h4 className="font-bold text-green-800 mb-2">Fresh & Effective</h4>
                  <ul className="text-sm text-gray-600 space-y-1 text-left">
                    <li>• Clear to pale yellow</li>
                    <li>• No strong odor</li>
                    <li>• Normal texture</li>
                    <li>• Full potency</li>
                  </ul>
                </div>

                <div className="text-center">
                  <div className="w-24 h-24 bg-yellow-50 rounded-full flex items-center justify-center mx-auto mb-4 border-4 border-yellow-100">
                    <div className="w-16 h-16 bg-amber-300 rounded-full"></div>
                  </div>
                  <h4 className="font-bold text-yellow-800 mb-2">Starting to Oxidize</h4>
                  <ul className="text-sm text-gray-600 space-y-1 text-left">
                    <li>• Light amber color</li>
                    <li>• Slight metallic smell</li>
                    <li>• Still usable but reduced efficacy</li>
                    <li>• Use quickly or refrigerate</li>
                  </ul>
                </div>

                <div className="text-center">
                  <div className="w-24 h-24 bg-yellow-50 rounded-full flex items-center justify-center mx-auto mb-4 border-4 border-yellow-100">
                    <div className="w-16 h-16 bg-amber-700 rounded-full"></div>
                  </div>
                  <h4 className="font-bold text-red-800 mb-2">Fully Oxidized</h4>
                  <ul className="text-sm text-gray-600 space-y-1 text-left">
                    <li>• Dark amber/brown color</li>
                    <li>• Strong unpleasant odor</li>
                    <li>• May cause irritation</li>
                    <li>• Discard immediately</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      case 'skincare-mistakes-beginners':
        return (
          <div className="my-12 space-y-8">
            {/* Common Mistakes Infographic */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Top 5 Beginner Skincare Mistakes
                </h3>
                <p className="text-gray-600">
                  Visual guide to common pitfalls and how to avoid them
                </p>
              </div>

              <div className="space-y-6">
                <div className="bg-red-50 rounded-lg p-6 border border-red-200">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                      1
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-red-800 mb-2">Using Too Many Products at Once</h4>
                      <p className="text-red-700 mb-3">Overwhelming your skin with multiple new products can cause irritation and make it impossible to identify what's working or causing problems.</p>
                      <div className="bg-white rounded-lg p-3">
                        <h5 className="font-medium text-red-800 mb-1">The Fix:</h5>
                        <ul className="text-sm text-red-700 space-y-1">
                          <li>• Start with basics: cleanser, moisturizer, sunscreen</li>
                          <li>• Add one new product every 2-3 weeks</li>
                          <li>• Keep a skincare diary to track what works</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-orange-50 rounded-lg p-6 border border-orange-200">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                      2
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-orange-800 mb-2">Expecting Overnight Results</h4>
                      <p className="text-orange-700 mb-3">Skin cell turnover takes 28-40 days, and real improvements take 6-12 weeks to show. Constantly switching products prevents you from seeing true results.</p>
                      <div className="bg-white rounded-lg p-3">
                        <h5 className="font-medium text-orange-800 mb-1">The Fix:</h5>
                        <ul className="text-sm text-orange-700 space-y-1">
                          <li>• Be patient - skincare is a marathon, not a sprint</li>
                          <li>• Take progress photos to track subtle changes</li>
                          <li>• Use products consistently for at least 6-8 weeks</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                      3
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-yellow-800 mb-2">Skipping Sunscreen</h4>
                      <p className="text-yellow-700 mb-3">80% of aging is caused by sun exposure. Skipping sunscreen undoes the benefits of your other skincare products and increases skin cancer risk.</p>
                      <div className="bg-white rounded-lg p-3">
                        <h5 className="font-medium text-yellow-800 mb-1">The Fix:</h5>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          <li>• Use SPF 30+ daily, even indoors and on cloudy days</li>
                          <li>• Apply generously (1/4 teaspoon for face)</li>
                          <li>• Reapply every 2 hours when outdoors</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                      4
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-green-800 mb-2">Over-Exfoliating</h4>
                      <p className="text-green-700 mb-3">Using scrubs daily or combining multiple exfoliating products damages the skin barrier, leading to sensitivity, irritation, and increased oil production.</p>
                      <div className="bg-white rounded-lg p-3">
                        <h5 className="font-medium text-green-800 mb-1">The Fix:</h5>
                        <ul className="text-sm text-green-700 space-y-1">
                          <li>• Limit physical scrubs to 1-2 times per week maximum</li>
                          <li>• Choose chemical exfoliants over harsh scrubs</li>
                          <li>• Start slowly with exfoliating acids (once a week)</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                      5
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-blue-800 mb-2">Mixing Incompatible Ingredients</h4>
                      <p className="text-blue-700 mb-3">Using retinol with vitamin C, or layering multiple acids without understanding interactions reduces effectiveness and increases irritation.</p>
                      <div className="bg-white rounded-lg p-3">
                        <h5 className="font-medium text-blue-800 mb-1">The Fix:</h5>
                        <ul className="text-sm text-blue-700 space-y-1">
                          <li>• Research ingredient interactions before combining</li>
                          <li>• Use vitamin C in AM, retinol in PM</li>
                          <li>• Separate acids by time or alternate days</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'hyaluronic-acid-molecular-weight':
        return (
          <div className="my-12 space-y-8">
            {/* Molecular Weight Comparison */}
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <BarChart3 className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Hyaluronic Acid Molecular Weight Comparison
                </h3>
                <p className="text-gray-600">
                  How different molecular weights affect skin penetration and benefits
                </p>
              </div>

              <div className="space-y-8">
                <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-bold text-blue-800">High Molecular Weight (1,500-1,800 kDa)</h4>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Surface Level</span>
                  </div>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <div className="bg-white rounded-lg p-4 mb-4">
                        <h5 className="font-medium text-blue-700 mb-2">Penetration Depth</h5>
                        <div className="relative h-20 bg-gray-100 rounded-lg overflow-hidden">
                          <div className="absolute top-0 left-0 w-full h-4 bg-blue-500 opacity-70"></div>
                          <div className="absolute bottom-0 left-0 w-full h-16 bg-gray-200"></div>
                          <div className="absolute top-4 left-0 w-full flex justify-center">
                            <span className="text-xs bg-white px-2 py-1 rounded-full shadow-sm">Stratum Corneum Only</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">Immediate plumping effect</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">Surface hydration</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="bg-white rounded-lg p-4 h-full">
                        <h5 className="font-medium text-blue-700 mb-2">Best For</h5>
                        <ul className="space-y-2 text-sm text-gray-700">
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Dehydrated skin needing immediate relief</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Fine lines from surface dryness</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Makeup prep for smoother application</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Quick hydration boost</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-6 border border-purple-200">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-bold text-purple-800">Medium Molecular Weight (50-1,500 kDa)</h4>
                    <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">Upper Layers</span>
                  </div>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <div className="bg-white rounded-lg p-4 mb-4">
                        <h5 className="font-medium text-purple-700 mb-2">Penetration Depth</h5>
                        <div className="relative h-20 bg-gray-100 rounded-lg overflow-hidden">
                          <div className="absolute top-0 left-0 w-full h-8 bg-purple-500 opacity-70"></div>
                          <div className="absolute bottom-0 left-0 w-full h-12 bg-gray-200"></div>
                          <div className="absolute top-8 left-0 w-full flex justify-center">
                            <span className="text-xs bg-white px-2 py-1 rounded-full shadow-sm">Upper Epidermis</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">Sustained hydration</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">Improved skin texture</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="bg-white rounded-lg p-4 h-full">
                        <h5 className="font-medium text-purple-700 mb-2">Best For</h5>
                        <ul className="space-y-2 text-sm text-gray-700">
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Daily maintenance hydration</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Normal to dry skin types</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>All-day moisture needs</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Balanced skincare routines</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-teal-50 rounded-lg p-6 border border-teal-200">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-bold text-teal-800">Low Molecular Weight (20-300 kDa)</h4>
                    <span className="bg-teal-100 text-teal-800 px-3 py-1 rounded-full text-sm">Deep Penetration</span>
                  </div>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <div className="bg-white rounded-lg p-4 mb-4">
                        <h5 className="font-medium text-teal-700 mb-2">Penetration Depth</h5>
                        <div className="relative h-20 bg-gray-100 rounded-lg overflow-hidden">
                          <div className="absolute top-0 left-0 w-full h-16 bg-teal-500 opacity-70"></div>
                          <div className="absolute bottom-0 left-0 w-full h-4 bg-gray-200"></div>
                          <div className="absolute top-16 left-0 w-full flex justify-center">
                            <span className="text-xs bg-white px-2 py-1 rounded-full shadow-sm">Deep Dermis</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">Deep, long-lasting hydration</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm text-gray-700">Stimulates collagen production</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="bg-white rounded-lg p-4 h-full">
                        <h5 className="font-medium text-teal-700 mb-2">Best For</h5>
                        <ul className="space-y-2 text-sm text-gray-700">
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Mature skin with deeper hydration needs</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Anti-aging routines</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Long-term skin improvement</span>
                          </li>
                          <li className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>Deeper wrinkles and loss of elasticity</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="my-12">
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
                  <BarChart3 className="w-8 h-8 text-brand-teal" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Visual Learning Guide
                </h3>
                <p className="text-gray-600 mb-6">
                  Interactive infographics and visual aids to enhance your understanding
                </p>
                <div className="bg-blue-50 rounded-lg p-6 max-w-md mx-auto">
                  <p className="text-blue-800">
                    Custom infographics for this article are being developed to provide 
                    visual learning aids and enhance comprehension of key concepts.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title={seoTitle}
        description={seoDescription}
        keywords={post.seoKeywords}
        canonicalUrl={canonicalUrl}
        structuredData={articleStructuredData}
      />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-4">
          <nav aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link to="/" className="text-gray-500 hover:text-brand-teal">Home</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link to="/blog" className="text-gray-500 hover:text-brand-teal">Blog</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-brand-charcoal font-medium">{post.title}</li>
            </ol>
          </nav>
          <Link
            to="/blog"
            className="inline-flex items-center space-x-2 text-brand-teal hover:text-brand-teal-dark transition-colors duration-200 mt-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Blog</span>
          </Link>
        </div>
      </div>

      {/* Article Header */}
      <div className="bg-white">
        <div className="container-custom py-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <span className="text-sm bg-brand-teal text-white px-3 py-1 rounded-full">
                {post.category}
              </span>
            </div>

            <h1 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-4 leading-tight">
              {post.title}
            </h1>

            <div className="flex items-center space-x-4 text-sm text-gray-600 mb-8">
              <span>By Skincare Compass</span>
              <span>{post.publishDate}</span>
              <span>{post.readTime}</span>
            </div>

            {/* Featured Image */}
            <div className="mb-6">
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-64 md:h-80 object-cover rounded-xl"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-4 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-3">
                <div className="prose prose-lg max-w-none">
                  <BlogContent content={post.content} />
                </div>

                {/* Infographics Section */}
                {renderInfographics()}
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-24 space-y-6">
                  <TableOfContents content={post.content} />

                  {/* Share Section */}
                  <div className="bg-gray-50 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-brand-charcoal mb-4">
                      Share
                    </h3>
                    <div className="flex space-x-3">
                      <button
                        className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                        title="Share on Facebook"
                      >
                        <Share2 className="w-4 h-4" />
                      </button>
                      <button
                        className="flex items-center justify-center w-10 h-10 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors duration-200"
                        title="Share on Twitter"
                      >
                        <Share2 className="w-4 h-4" />
                      </button>
                      <button
                        className="flex items-center justify-center w-10 h-10 bg-blue-800 text-white rounded-lg hover:bg-blue-900 transition-colors duration-200"
                        title="Share on LinkedIn"
                      >
                        <Share2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tags */}
      <div className="py-8 bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center space-x-2 mb-4">
              <Tag className="w-5 h-5 text-gray-400" />
              <span className="text-gray-600 font-medium">Tags:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag, index) => (
                <span
                  key={index}
                  className="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-full hover:bg-brand-teal hover:text-white transition-colors duration-200 cursor-pointer"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Related Articles */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-brand-charcoal mb-8 text-center">
              Related Articles
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost, index) => (
                <Link
                  key={index}
                  to={`/blog/${relatedPost.slug}`}
                  className="card overflow-hidden hover:scale-105 transition-all duration-300 group"
                >
                  <div className="relative">
                    <img
                      src={relatedPost.image}
                      alt={relatedPost.title}
                      className="w-full h-40 object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <span className="text-xs bg-white/90 backdrop-blur-sm text-gray-700 px-2 py-1 rounded-full">
                        {relatedPost.category}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <h4 className="text-lg font-semibold text-brand-charcoal mb-2 group-hover:text-brand-teal transition-colors duration-200 line-clamp-2">
                      {relatedPost.title}
                    </h4>
                    
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {relatedPost.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{relatedPost.readTime}</span>
                      <ArrowRight className="w-4 h-4 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter CTA */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
                <BookOpen className="w-8 h-8 text-brand-teal" />
              </div>
              <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
                Enjoy this article?
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Subscribe to our newsletter for weekly skincare science, ingredient spotlights, and expert advice delivered straight to your inbox.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
                <button className="btn-primary whitespace-nowrap">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogPostPage;