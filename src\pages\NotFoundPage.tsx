import React from 'react';
import { Link } from 'react-router-dom';
import { Home, Search, ArrowLeft, Compass } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const NotFoundPage: React.FC = () => {
  const popularPages = [
    {
      title: 'Ingredient Directory',
      description: 'Browse our comprehensive database of skincare ingredients',
      href: '/ingredients',
      icon: Search,
    },
    {
      title: 'Combination Guides',
      description: 'Learn which ingredients work well together',
      href: '/combinations',
      icon: Compass,
    },
    {
      title: "Beginner's Hub",
      description: 'Start your skincare journey with expert guidance',
      href: '/beginners',
      icon: Home,
    },
  ];

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white flex items-center">
      <MetaTags
        title="Page Not Found - 404 Error"
        description="The page you're looking for doesn't exist. Find what you need with our helpful navigation and search tools."
        canonicalUrl="https://www.skincarecompass.com/404"
        noIndex={true}
      />
      
      <div className="container-custom">
        <div className="max-w-4xl mx-auto text-center">
          {/* 404 Illustration */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-32 h-32 bg-brand-teal/10 rounded-full mb-6">
              <span className="text-6xl font-bold text-brand-teal">404</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Page Not Found
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Oops! The page you're looking for seems to have wandered off. 
              Don't worry, we'll help you find your way back to the good stuff.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link to="/" className="btn-primary flex items-center space-x-2">
              <Home className="w-5 h-5" />
              <span>Go Home</span>
            </Link>
            <button 
              onClick={() => window.history.back()} 
              className="btn-secondary flex items-center space-x-2"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Go Back</span>
            </button>
          </div>

          {/* Popular Pages */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-brand-charcoal mb-8">
              Popular Pages
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              {popularPages.map((page, index) => (
                <Link
                  key={index}
                  to={page.href}
                  className="card p-6 hover:scale-105 transition-all duration-200 group"
                  aria-label={`Go to ${page.title}`}
                >
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-brand-teal/10 rounded-lg mb-4 group-hover:bg-brand-teal group-hover:text-white transition-colors duration-200">
                    <page.icon className="w-6 h-6 text-brand-teal group-hover:text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-2 group-hover:text-brand-teal transition-colors duration-200">
                    {page.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {page.description}
                  </p>
                </Link>
              ))}
            </div>
          </div>

          {/* Search */}
          <div className="max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-brand-charcoal mb-4">
              Or search for what you need
            </h3>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search ingredients, guides, tools..."
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                aria-label="Search site content"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;