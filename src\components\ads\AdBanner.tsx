import React, { useEffect, useRef, useState } from 'react';
import { loadAdScript, isAdBlockerActive, validateAdConfig } from '../../utils/adUtils';

interface AdBannerProps {
  width: number;
  height: number;
  adKey: string;
  className?: string;
  fallbackContent?: React.ReactNode;
}

const AdBanner: React.FC<AdBannerProps> = ({
  width,
  height,
  adKey,
  className = '',
  fallbackContent
}) => {
  const adRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isAdBlockerDetected, setIsAdBlockerDetected] = useState(false);

  useEffect(() => {
    if (!adRef.current) return;

    // Check for ad blocker
    if (isAdBlockerActive()) {
      setIsAdBlockerDetected(true);
      setHasError(true);
      return;
    }

    // Validate configuration
    const config = { key: adKey, format: 'iframe' as const, width, height };
    if (!validateAdConfig(config)) {
      console.error('Invalid ad configuration:', config);
      setHasError(true);
      return;
    }

    // Clear any existing content
    adRef.current.innerHTML = '';

    // Load ad script
    loadAdScript(config)
      .then(() => setIsLoaded(true))
      .catch((error) => {
        console.error('Error loading ad:', error);
        setHasError(true);
      });
  }, [adKey, width, height]);

  if (hasError) {
    if (fallbackContent) {
      return <div className={className}>{fallbackContent}</div>;
    }

    return (
      <div
        className={`ad-banner ${className} bg-gray-50 border border-gray-200 flex items-center justify-center`}
        style={{ width: `${width}px`, height: `${height}px` }}
      >
        <div className="text-center text-gray-500 text-sm">
          {isAdBlockerDetected ? 'Ad blocked' : 'Ad unavailable'}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`ad-banner ${className}`}
      style={{ width: `${width}px`, height: `${height}px` }}
    >
      <div ref={adRef} />
      {!isLoaded && !hasError && (
        <div
          className="flex items-center justify-center bg-gray-100 text-gray-500 text-sm animate-pulse"
          style={{ width: `${width}px`, height: `${height}px` }}
        >
          Loading ad...
        </div>
      )}
    </div>
  );
};

export default AdBanner;
