import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Wrench, CheckCircle, Users, BarChart3, Search, Calculator, ArrowRight, Star, Microscope, Calendar } from 'lucide-react';
import SkincareIngredientsWheel from '../components/infographics/SkincareIngredientsWheel';
import MetaTags from '../components/SEO/MetaTags';

const ToolsPage: React.FC = () => {
  const tools = [
    {
      slug: 'ingredient-compatibility-checker',
      title: 'Ingredient Compatibility Checker',
      description: 'Check if your skincare ingredients work well together or if there are any potential conflicts in your routine.',
      icon: CheckCircle,
      color: 'bg-green-500',
      features: ['Real-time compatibility analysis', 'Detailed explanations', 'Alternative suggestions', 'Save your combinations'],
      difficulty: 'Beginner',
      users: '25K+',
      rating: 4.8,
      isPopular: true,
    },
    {
      slug: 'routine-builder-quiz',
      title: 'Personalized Routine Builder',
      description: 'Take our comprehensive quiz to get a customized skincare routine based on your skin type, concerns, and lifestyle.',
      icon: Users,
      color: 'bg-blue-500',
      features: ['Personalized recommendations', 'Product suggestions', 'Step-by-step routine', 'Budget considerations'],
      difficulty: 'Beginner',
      users: '18K+',
      rating: 4.7,
      isPopular: true,
    },
    {
      slug: 'ingredient-analyzer',
      title: 'Product Ingredient Analyzer',
      description: 'Paste any ingredient list and get a detailed breakdown of each component, including benefits and potential concerns.',
      icon: Search,
      color: 'bg-purple-500',
      features: ['Instant ingredient analysis', 'Benefit explanations', 'Concern flagging', 'Ingredient ratings'],
      difficulty: 'Intermediate',
      users: '12K+',
      rating: 4.6,
      isPopular: false,
    },
    {
      slug: 'concentration-calculator',
      title: 'Active Concentration Calculator',
      description: 'Calculate the effective concentration of active ingredients in your products and optimize your routine strength.',
      icon: Calculator,
      color: 'bg-orange-500',
      features: ['Concentration calculations', 'Dosage recommendations', 'Safety guidelines', 'Progress tracking'],
      difficulty: 'Advanced',
      users: '8K+',
      rating: 4.5,
      isPopular: false,
    },
    {
      slug: 'routine-tracker',
      title: 'Skincare Routine Tracker',
      description: 'Track your daily routine, monitor skin changes, and identify which products are working best for you.',
      icon: BarChart3,
      color: 'bg-teal-500',
      features: ['Daily routine logging', 'Progress photos', 'Skin condition tracking', 'Product effectiveness analysis'],
      difficulty: 'Beginner',
      users: '15K+',
      rating: 4.4,
      isPopular: true,
    },
    {
      slug: 'ph-compatibility-tool',
      title: 'pH Compatibility Tool',
      description: 'Ensure your products work together by checking pH compatibility and optimal layering order.',
      icon: CheckCircle,
      color: 'bg-pink-500',
      features: ['pH level checking', 'Layering recommendations', 'Wait time suggestions', 'Efficacy optimization'],
      difficulty: 'Advanced',
      users: '6K+',
      rating: 4.3,
      isPopular: false,
    },
    {
      slug: 'ingredient-analyzer',
      title: 'Skincare Ingredient Analyzer',
      description: 'Analyze any skincare ingredient for safety, effectiveness, and compatibility with your skin type.',
      icon: Microscope,
      color: 'bg-purple-500',
      features: ['Ingredient safety analysis', 'Effectiveness ratings', 'Skin type compatibility', 'Scientific research data'],
      difficulty: 'Beginner',
      users: '12K+',
      rating: 4.6,
      isPopular: true,
    },
    {
      slug: 'concentration-calculator',
      title: 'Active Concentration Calculator',
      description: 'Calculate optimal concentrations for skincare actives and avoid over-dosing your skin.',
      icon: Calculator,
      color: 'bg-blue-500',
      features: ['Concentration calculations', 'Dosage recommendations', 'Safety thresholds', 'Progressive increase plans'],
      difficulty: 'Intermediate',
      users: '8K+',
      rating: 4.5,
      isPopular: false,
    },
    {
      slug: 'routine-tracker',
      title: 'Skincare Routine Tracker',
      description: 'Track your daily skincare routine, monitor progress, and get personalized insights.',
      icon: Calendar,
      color: 'bg-indigo-500',
      features: ['Daily routine logging', 'Progress photos', 'Skin condition tracking', 'Personalized insights'],
      difficulty: 'Beginner',
      users: '15K+',
      rating: 4.7,
      isPopular: true,
    },
  ];

  const categories = [
    { name: 'All Tools', count: tools.length, active: true },
    { name: 'Beginner', count: tools.filter(t => t.difficulty === 'Beginner').length, active: false },
    { name: 'Intermediate', count: tools.filter(t => t.difficulty === 'Intermediate').length, active: false },
    { name: 'Advanced', count: tools.filter(t => t.difficulty === 'Advanced').length, active: false },
    { name: 'Popular', count: tools.filter(t => t.isPopular).length, active: false },
  ];

  const quickStats = [
    {
      icon: Users,
      stat: '95K+',
      label: 'Tool Users',
    },
    {
      icon: CheckCircle,
      stat: '750K+',
      label: 'Analyses Run',
    },
    {
      icon: Star,
      stat: '4.6',
      label: 'Average Rating',
    },
  ];

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Free Skincare Tools & Calculators - Ingredient Analyzer, Routine Tracker & More"
        description="Use our comprehensive skincare tools: ingredient analyzer, concentration calculator, routine tracker, pH compatibility tool, and more. Get personalized skincare analysis and recommendations."
        keywords="skincare tools, ingredient analyzer, skincare ingredient checker, concentration calculator, routine tracker, pH compatibility tool, skincare calculator, ingredient compatibility checker, skincare analysis tools, personalized skincare recommendations"
        canonicalUrl="https://www.skincarecompass.com/tools"
      />
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-12">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
              <Wrench className="w-8 h-8 text-brand-teal" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Free Skincare Tools & Calculators
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Analyze ingredients, calculate concentrations, track routines, and optimize your skincare with our comprehensive suite of science-backed tools.
              Get personalized recommendations and professional-grade analysis for free.
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-6 max-w-2xl mx-auto">
            {quickStats.map((stat, index) => (
              <div key={index} className="text-center animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="inline-flex items-center justify-center w-12 h-12 bg-brand-teal/10 rounded-full mb-3">
                  <stat.icon className="w-6 h-6 text-brand-teal" />
                </div>
                <div className="text-2xl font-bold text-brand-charcoal">{stat.stat}</div>
                <div className="text-gray-600 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Ingredients Wheel */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <SkincareIngredientsWheel />
        </div>
      </div>

      {/* Categories */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-6">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                  category.active
                    ? 'bg-brand-teal text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tools Grid */}
      <div className="section-padding">
        <div className="container-custom">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {tools.map((tool, index) => (
              <Link
                key={tool.slug}
                to={`/tools/${tool.slug}`}
                className="card p-6 hover:scale-105 transition-all duration-200 group animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className={`inline-flex items-center justify-center w-12 h-12 ${tool.color} rounded-lg group-hover:scale-110 transition-transform duration-200`}>
                    <tool.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-right">
                    {tool.isPopular && (
                      <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full mb-2 block">
                        Popular
                      </span>
                    )}
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm text-gray-600">{tool.rating}</span>
                    </div>
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-brand-charcoal mb-3 group-hover:text-brand-teal transition-colors duration-200">
                  {tool.title}
                </h3>

                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {tool.description}
                </p>

                <div className="space-y-3 mb-4">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    Key Features
                  </div>
                  <ul className="space-y-1">
                    {tool.features.slice(0, 3).map((feature, idx) => (
                      <li key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-brand-teal rounded-full"></div>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      tool.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                      tool.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {tool.difficulty}
                    </span>
                    <span>{tool.users} users</span>
                  </div>
                  <ArrowRight className="w-5 h-5 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Featured Tool */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              <div>
                <div className="inline-flex items-center space-x-2 mb-4">
                  <CheckCircle className="w-6 h-6 text-brand-teal" />
                  <span className="text-sm font-medium text-brand-teal">Most Popular Tool</span>
                </div>
                <h3 className="text-3xl font-bold text-brand-charcoal mb-4">
                  Ingredient Compatibility Checker
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Our most popular tool helps you avoid ingredient conflicts and maximize the 
                  effectiveness of your skincare routine. Used by over 25,000 skincare enthusiasts.
                </p>
                <div className="flex items-center space-x-6 mb-6">
                  <div className="flex items-center space-x-1">
                    <Star className="w-5 h-5 text-yellow-500 fill-current" />
                    <span className="font-semibold">4.8/5</span>
                    <span className="text-gray-600">(2,400 reviews)</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-600">25K+ users</span>
                  </div>
                </div>
                <Link to="/tools/ingredient-compatibility-checker" className="btn-primary">
                  Try It Now - Free
                </Link>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <span className="font-medium">Vitamin C + Niacinamide</span>
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                    <span className="font-medium">Retinol + AHA</span>
                    <div className="w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">!</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                    <span className="font-medium">Vitamin C + Retinol</span>
                    <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">×</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-4">
              Need Help Choosing the Right Tool?
            </h2>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Not sure which tool is right for your needs? Our tool recommendation quiz 
              will help you find the perfect match for your skincare goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                Take Tool Quiz
              </button>
              <Link to="/beginners" className="btn-secondary">
                Start with Basics
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolsPage;