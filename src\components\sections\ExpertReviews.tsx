import React, { useState } from 'react';
import { Star, User, Calendar, ThumbsUp, MessageCircle, Award, Verified } from 'lucide-react';

interface ExpertReview {
  id: string;
  expertName: string;
  credentials: string;
  rating: number;
  title: string;
  content: string;
  date: string;
  likes: number;
  replies: number;
  verified: boolean;
  specialty: string;
  avatar?: string;
  helpfulVotes: number;
  totalVotes: number;
}

interface ExpertReviewsProps {
  productType: 'ingredient' | 'combination' | 'routine';
  productId: string;
  className?: string;
}

const ExpertReviews: React.FC<ExpertReviewsProps> = ({ 
  productType, 
  productId, 
  className = '' 
}) => {
  const [sortBy, setSortBy] = useState<'newest' | 'rating' | 'helpful'>('helpful');
  const [filterBy, setFilterBy] = useState<'all' | 'dermatologist' | 'chemist' | 'researcher'>('all');

  // Sample expert reviews data
  const expertReviews: ExpertReview[] = [
    {
      id: '1',
      expertName: 'Dr. <PERSON>',
      credentials: 'MD, Board-Certified Dermatologist',
      rating: 5,
      title: 'Excellent evidence-based information on hyaluronic acid',
      content: 'This comprehensive guide on hyaluronic acid provides accurate, science-based information that aligns with current dermatological research. The explanation of different molecular weights and their penetration capabilities is particularly well-done. I regularly refer patients to this resource for understanding HA benefits and proper application techniques.',
      date: '2024-01-15',
      likes: 127,
      replies: 8,
      verified: true,
      specialty: 'Cosmetic Dermatology',
      helpfulVotes: 89,
      totalVotes: 95
    },
    {
      id: '2',
      expertName: 'Prof. Michael Rodriguez',
      credentials: 'PhD, Cosmetic Chemistry',
      rating: 4,
      title: 'Solid formulation science, minor technical details could be expanded',
      content: 'The content accurately describes HA\'s mechanism of action and formulation considerations. The discussion of molecular weight distribution is scientifically sound. Would benefit from more detail on stabilization methods and pH considerations in formulations, but overall an excellent resource for consumers.',
      date: '2024-01-10',
      likes: 94,
      replies: 12,
      verified: true,
      specialty: 'Cosmetic Chemistry',
      helpfulVotes: 76,
      totalVotes: 82
    },
    {
      id: '3',
      expertName: 'Dr. Emily Watson',
      credentials: 'PhD, Biochemistry & Skin Research',
      rating: 5,
      title: 'Outstanding clinical research integration',
      content: 'Impressed by the thorough integration of clinical studies and peer-reviewed research. The safety profile section is comprehensive and the contraindications are appropriately highlighted. This level of evidence-based content is rare in consumer skincare education.',
      date: '2024-01-08',
      likes: 156,
      replies: 15,
      verified: true,
      specialty: 'Clinical Research',
      helpfulVotes: 134,
      totalVotes: 142
    }
  ];

  const getSpecialtyIcon = (specialty: string) => {
    switch (specialty) {
      case 'Cosmetic Dermatology':
        return <User className="w-4 h-4" />;
      case 'Cosmetic Chemistry':
        return <Award className="w-4 h-4" />;
      case 'Clinical Research':
        return <MessageCircle className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  const getSpecialtyColor = (specialty: string) => {
    switch (specialty) {
      case 'Cosmetic Dermatology':
        return 'bg-blue-100 text-blue-800';
      case 'Cosmetic Chemistry':
        return 'bg-purple-100 text-purple-800';
      case 'Clinical Research':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const averageRating = expertReviews.reduce((sum, review) => sum + review.rating, 0) / expertReviews.length;
  const totalReviews = expertReviews.length;

  return (
    <div className={`bg-white rounded-2xl p-6 shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-2xl font-bold text-brand-charcoal mb-2">Expert Reviews</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="flex items-center">
                {renderStars(Math.round(averageRating))}
              </div>
              <span className="text-lg font-semibold text-brand-charcoal">
                {averageRating.toFixed(1)}
              </span>
              <span className="text-gray-600">({totalReviews} expert reviews)</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Verified className="w-5 h-5 text-brand-teal" />
          <span className="text-sm text-brand-teal font-medium">Verified Experts Only</span>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium text-gray-700">Sort by:</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-1 border border-gray-200 rounded text-sm focus:outline-none focus:ring-2 focus:ring-brand-teal"
          >
            <option value="helpful">Most Helpful</option>
            <option value="newest">Newest</option>
            <option value="rating">Highest Rating</option>
          </select>
        </div>
        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium text-gray-700">Filter by:</label>
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value as any)}
            className="px-3 py-1 border border-gray-200 rounded text-sm focus:outline-none focus:ring-2 focus:ring-brand-teal"
          >
            <option value="all">All Experts</option>
            <option value="dermatologist">Dermatologists</option>
            <option value="chemist">Cosmetic Chemists</option>
            <option value="researcher">Researchers</option>
          </select>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-6">
        {expertReviews.map((review) => (
          <div key={review.id} className="border border-gray-200 rounded-lg p-6">
            {/* Expert Info */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start space-x-3">
                <div className="w-12 h-12 bg-brand-teal/10 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-brand-teal" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h4 className="font-semibold text-brand-charcoal">{review.expertName}</h4>
                    {review.verified && (
                      <Verified className="w-4 h-4 text-brand-teal" />
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{review.credentials}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getSpecialtyColor(review.specialty)}`}>
                      {getSpecialtyIcon(review.specialty)}
                      <span>{review.specialty}</span>
                    </span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-1 mb-1">
                  {renderStars(review.rating)}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(review.date).toLocaleDateString()}
                </div>
              </div>
            </div>

            {/* Review Content */}
            <div className="mb-4">
              <h5 className="font-semibold text-brand-charcoal mb-2">{review.title}</h5>
              <p className="text-gray-700 leading-relaxed">{review.content}</p>
            </div>

            {/* Review Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-2 text-gray-600 hover:text-brand-teal transition-colors">
                  <ThumbsUp className="w-4 h-4" />
                  <span className="text-sm">Helpful ({review.helpfulVotes})</span>
                </button>
                <button className="flex items-center space-x-2 text-gray-600 hover:text-brand-teal transition-colors">
                  <MessageCircle className="w-4 h-4" />
                  <span className="text-sm">Reply ({review.replies})</span>
                </button>
              </div>
              <div className="text-sm text-gray-500">
                {review.helpfulVotes} of {review.totalVotes} found this helpful
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Call to Action */}
      <div className="mt-8 p-6 bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-lg text-center">
        <h4 className="text-lg font-semibold text-brand-charcoal mb-2">
          Are you a skincare professional?
        </h4>
        <p className="text-gray-600 mb-4">
          Join our expert review program and help educate consumers with evidence-based insights.
        </p>
        <button className="btn-primary">
          Apply for Expert Status
        </button>
      </div>
    </div>
  );
};

export default ExpertReviews;
