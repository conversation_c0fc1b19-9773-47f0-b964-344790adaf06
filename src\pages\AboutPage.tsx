import React from 'react';
import { BookO<PERSON>, Heart, Target, Eye } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const AboutPage: React.FC = () => {
  const values = [
    {
      icon: BookOpen,
      title: 'Science-Based Education',
      description: 'Every piece of content is backed by peer-reviewed research and expert knowledge.',
    },
    {
      icon: Heart,
      title: 'Inclusive Community',
      description: 'We believe skincare should be accessible to everyone, regardless of background or budget.',
    },
    {
      icon: Target,
      title: 'Practical Application',
      description: 'We bridge the gap between complex science and real-world skincare routines.',
    },
    {
      icon: Eye,
      title: 'Transparency',
      description: 'Clear, honest information without marketing hype or hidden agendas.',
    },
  ];

  const stats = [
    { number: '2.5M+', label: 'Ingredients Analyzed' },
    { number: '500K+', label: 'Users Helped' },
    { number: 'A-Z', label: 'Ingredients' },
    { number: '10+', label: 'Combinations' },
  ];

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="About Skincare Compass - Our Mission & Expert Team"
        description="Learn about Skincare Compass, our mission to make skincare science accessible, and meet our team of skincare experts and dermatologists."
        keywords="about skincare compass, skincare experts, dermatology team, skincare science, skincare education mission"
        canonicalUrl="https://www.skincarecompass.com/about"
      />
      {/* Hero Section */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-6">
              About Skincare Compass
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed mb-8">
              We're on a mission to make skincare science accessible, understandable, and actionable 
              for everyone. Founded by skincare experts, we believe that informed 
              decisions lead to better skin health.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-brand-teal">{stat.number}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mission & Vision */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-brand-charcoal mb-6">Our Mission</h2>
              <p className="text-gray-600 leading-relaxed mb-6">
                To democratize skincare knowledge by providing scientifically accurate, 
                easy-to-understand information about ingredients, combinations, and routines. 
                We believe everyone deserves access to expert-level skincare education.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Our platform bridges the gap between complex dermatological research and 
                practical daily skincare, empowering users to make informed decisions 
                about their skin health.
              </p>
            </div>
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-4">Our Vision</h3>
              <p className="text-gray-600 leading-relaxed">
                A world where everyone has the knowledge and confidence to care for their 
                skin effectively, where skincare decisions are based on science rather than 
                marketing, and where the beauty industry prioritizes education and transparency.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Values */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-brand-charcoal mb-12 text-center">
            Our Core Values
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
                  <value.icon className="w-8 h-8 text-brand-teal" />
                </div>
                <h3 className="text-lg font-semibold text-brand-charcoal mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Story */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Our Story
            </h2>
            <div className="prose prose-lg max-w-none text-gray-600">
              <p className="mb-6">
                Skincare Compass was born from a simple observation: despite the abundance of 
                skincare information online, most people still felt confused and overwhelmed 
                when trying to build an effective routine.
              </p>
              <p className="mb-6">
                Our founders noticed that consumers often came to appointments with bags full of products 
                that didn't work together, or worse, were causing irritation. The disconnect between 
                scientific knowledge and consumer understanding was clear.
              </p>
              <p className="mb-6">
                In 2023, we created a platform that would translate complex dermatological research 
                into actionable, easy-to-understand guidance. What started as a small database of 
                ingredient interactions has grown into the comprehensive resource you see today.
              </p>
              <p>
                Today, Skincare Compass serves over 500,000 users worldwide, helping them 
                navigate the complex world of skincare with confidence and scientific backing. 
                We're proud to be the bridge between laboratory research and your bathroom cabinet.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
              Join Our Mission
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Whether you're a skincare beginner or an enthusiast, we're here to support 
              your journey with science-backed information and a welcoming community.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                Start Learning
              </button>
              <button className="btn-secondary">
                Join Community
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;