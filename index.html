<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />



  <!-- Basic Meta Tags - Dynamic content will be handled by <PERSON><PERSON> Helmet -->
  <meta name="author" content="Skincare Compass" />
  <meta name="language" content="English" />

  <!-- Preconnect for Performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Structured Data - Organization -->
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Skincare Compass",
      "url": "https://www.skincarecompass.com",
      "logo": "https://www.skincarecompass.com/logo.png",
      "description": "The world's most trusted resource for skincare ingredient information and education",
      "foundingDate": "2023",
      "founders": [
        {
          "@type": "Person",
          "name": "Dr. Sarah Chen",
          "jobTitle": "Chief Medical Officer"
        }
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "email": "<EMAIL>"
      }
    }
    </script>

  <!-- Structured Data - Website -->
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Skincare Compass",
      "url": "https://www.skincarecompass.com",
      "description": "The ultimate resource for skincare ingredient information, combination guides, and beginner-friendly education"
    }
    </script>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>