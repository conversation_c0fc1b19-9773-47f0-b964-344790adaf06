# Image Assets Needed

## Required Images for Production:

1. **og-image.jpg** (1200x630px)
   - Use the design from `og-image.html` 
   - Convert to JPG format
   - Optimize for web (under 300KB)

2. **logo.png** (200x200px or larger)
   - Use the design from `logo.svg`
   - Convert to PNG with transparency
   - Create multiple sizes if needed (32x32, 64x64, 128x128, 200x200)

## How to Create:

1. Open `og-image.html` in browser
2. Take screenshot or use tools like:
   - Puppeteer for automated generation
   - Online HTML to image converters
   - Design tools like Figma/Canva

3. For logo.png:
   - Export `logo.svg` as PNG
   - Or recreate in design tool

## Temporary Solution:
- The website will work without these images
- Social media previews will use default images
- Structured data will reference the files but they won't break functionality
