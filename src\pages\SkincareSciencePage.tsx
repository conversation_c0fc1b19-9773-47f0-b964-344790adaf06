import React from 'react';
import { Microscope, BookOpen, Beaker, GraduationCap, TrendingUp, Users } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const SkincareSciencePage: React.FC = () => {
  const scienceTopics = [
    {
      icon: Microscope,
      title: 'Skin Biology & Anatomy',
      description: 'Understanding the structure and function of skin layers, barrier function, and cellular processes.',
      articles: [
        'The Science of Skin Barrier Function',
        'Understanding Skin pH and Its Impact',
        'Cell Turnover: The 28-Day Cycle Explained',
        'Collagen and Elastin: The Building Blocks of Youthful Skin'
      ],
      readTime: '45 min total',
      difficulty: 'Intermediate'
    },
    {
      icon: Beaker,
      title: 'Ingredient Chemistry',
      description: 'Deep dive into how skincare ingredients work at the molecular level and interact with skin.',
      articles: [
        'Molecular Weight and Skin Penetration',
        'pH and Chemical Stability in Formulations',
        'Antioxidant Mechanisms in Skincare',
        'The Science of Retinoid Conversion'
      ],
      readTime: '60 min total',
      difficulty: 'Advanced'
    },
    {
      icon: TrendingUp,
      title: 'Clinical Research',
      description: 'Evidence-based analysis of skincare ingredients and treatments backed by peer-reviewed studies.',
      articles: [
        'How to Read Skincare Studies',
        'Clinical Trial Design in Dermatology',
        'Statistical Significance vs. Clinical Relevance',
        'Meta-Analysis of Popular Ingredients'
      ],
      readTime: '50 min total',
      difficulty: 'Advanced'
    },
    {
      icon: GraduationCap,
      title: 'Dermatology Fundamentals',
      description: 'Professional-level knowledge about skin conditions, treatments, and dermatological principles.',
      articles: [
        'Acne Pathophysiology and Treatment',
        'Aging: Intrinsic vs. Extrinsic Factors',
        'Hyperpigmentation Mechanisms',
        'Sensitive Skin: Causes and Management'
      ],
      readTime: '55 min total',
      difficulty: 'Professional'
    }
  ];

  const researchHighlights = [
    {
      title: 'Latest Breakthrough: Bakuchiol vs. Retinol',
      summary: 'New clinical data shows bakuchiol provides comparable anti-aging benefits to retinol with significantly less irritation.',
      journal: 'Journal of Cosmetic Dermatology',
      year: '2024',
      significance: 'High'
    },
    {
      title: 'Microbiome and Skincare: New Frontiers',
      summary: 'Research reveals how skincare ingredients affect the skin microbiome and implications for product development.',
      journal: 'Nature Reviews Dermatology',
      year: '2024',
      significance: 'Revolutionary'
    },
    {
      title: 'Peptides in Anti-Aging: Mechanism Clarified',
      summary: 'Comprehensive study explains how different peptide types stimulate collagen production through distinct pathways.',
      journal: 'Dermatologic Surgery',
      year: '2024',
      significance: 'High'
    }
  ];

  const expertInsights = [
    {
      expert: 'Dr. Sarah Chen, Dermatologist',
      insight: 'The future of skincare lies in personalized formulations based on individual skin microbiome analysis.',
      specialty: 'Cosmetic Dermatology'
    },
    {
      expert: 'Prof. Michael Rodriguez, Biochemist',
      insight: 'Understanding molecular interactions is key to developing more effective, less irritating skincare formulations.',
      specialty: 'Cosmetic Chemistry'
    },
    {
      expert: 'Dr. Lisa Park, Research Scientist',
      insight: 'Clinical research is moving toward longer-term studies that better reflect real-world skincare usage patterns.',
      specialty: 'Clinical Research'
    }
  ];

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Skincare Science Hub - Evidence-Based Research & Expert Analysis"
        description="Explore the science behind skincare with expert analysis, clinical research summaries, and evidence-based ingredient studies. Your trusted source for skincare science education."
        keywords="skincare science, dermatology research, cosmetic chemistry, clinical studies, evidence-based skincare, skin biology"
        canonicalUrl="https://www.skincarecompass.com/science"
      />

      {/* Hero Section */}
      <div className="section-padding bg-gradient-to-br from-brand-teal to-brand-sage text-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Skincare Science Hub
            </h1>
            <p className="text-xl leading-relaxed mb-8 opacity-90">
              Dive deep into the science behind skincare with evidence-based research, 
              expert analysis, and comprehensive studies that inform better skincare decisions.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold">500+</div>
                <div className="text-sm opacity-80">Studies Analyzed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">50+</div>
                <div className="text-sm opacity-80">Expert Contributors</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">25+</div>
                <div className="text-sm opacity-80">Research Topics</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">100K+</div>
                <div className="text-sm opacity-80">Readers Educated</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Science Topics */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-brand-charcoal mb-12 text-center">
            Explore Skincare Science Topics
          </h2>
          <div className="grid lg:grid-cols-2 gap-8">
            {scienceTopics.map((topic, index) => (
              <div key={index} className="bg-brand-off-white rounded-2xl p-8 hover:shadow-lg transition-shadow">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="bg-brand-teal/10 p-3 rounded-lg">
                    <topic.icon className="w-6 h-6 text-brand-teal" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-brand-charcoal mb-2">{topic.title}</h3>
                    <p className="text-gray-600 mb-4">{topic.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                      <span className="flex items-center space-x-1">
                        <BookOpen className="w-4 h-4" />
                        <span>{topic.readTime}</span>
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        topic.difficulty === 'Beginner' ? 'bg-green-100 text-green-700' :
                        topic.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-700' :
                        topic.difficulty === 'Advanced' ? 'bg-orange-100 text-orange-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {topic.difficulty}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  {topic.articles.map((article, articleIndex) => (
                    <div key={articleIndex} className="flex items-center space-x-2 text-sm text-gray-600">
                      <div className="w-1.5 h-1.5 bg-brand-teal rounded-full"></div>
                      <span>{article}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Research Highlights */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-brand-charcoal mb-12 text-center">
            Latest Research Highlights
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {researchHighlights.map((research, index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    research.significance === 'Revolutionary' ? 'bg-purple-100 text-purple-700' :
                    research.significance === 'High' ? 'bg-blue-100 text-blue-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {research.significance} Impact
                  </span>
                  <span className="text-sm text-gray-500">{research.year}</span>
                </div>
                <h3 className="text-lg font-bold text-brand-charcoal mb-3">{research.title}</h3>
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">{research.summary}</p>
                <div className="text-xs text-gray-500 italic">{research.journal}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Expert Insights */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-brand-charcoal mb-12 text-center">
            Expert Insights
          </h2>
          <div className="space-y-8">
            {expertInsights.map((item, index) => (
              <div key={index} className="bg-brand-off-white rounded-2xl p-8">
                <div className="flex items-start space-x-4">
                  <div className="bg-brand-teal/10 p-3 rounded-full">
                    <Users className="w-6 h-6 text-brand-teal" />
                  </div>
                  <div className="flex-1">
                    <blockquote className="text-lg text-gray-700 italic mb-4">
                      "{item.insight}"
                    </blockquote>
                    <div className="flex items-center space-x-4">
                      <div>
                        <div className="font-semibold text-brand-charcoal">{item.expert}</div>
                        <div className="text-sm text-gray-500">{item.specialty}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="section-padding bg-brand-teal text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold mb-6">
            Stay Updated with Latest Research
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Get notified when we publish new research analysis, expert insights, 
            and evidence-based skincare guides.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-white"
            />
            <button className="bg-white text-brand-teal px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkincareSciencePage;
