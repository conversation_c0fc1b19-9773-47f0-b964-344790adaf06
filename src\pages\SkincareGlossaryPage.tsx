import React, { useState } from 'react';
import { Search, BookOpen, Filter, Star } from 'lucide-react';
import MetaTags from '../components/SEO/MetaTags';

const SkincareGlossaryPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Terms' },
    { id: 'ingredients', name: 'Ingredients' },
    { id: 'conditions', name: 'Skin Conditions' },
    { id: 'treatments', name: 'Treatments' },
    { id: 'terminology', name: 'General Terms' },
  ];

  const glossaryTerms = [
    {
      term: 'Acne',
      category: 'conditions',
      definition: 'A common skin condition that occurs when hair follicles become plugged with oil and dead skin cells, resulting in whiteheads, blackheads, or pimples.',
      relatedTerms: ['Comedone', 'Sebum', 'Follicle'],
      isPopular: true,
    },
    {
      term: 'Alpha Hydroxy Acids (AHAs)',
      category: 'ingredients',
      definition: 'Water-soluble acids derived from natural sources that exfoliate the skin surface, improve texture, and reduce signs of aging. Common types include glycolic acid and lactic acid.',
      relatedTerms: ['Glycolic Acid', 'Lactic Acid', 'Chemical Exfoliation'],
      isPopular: true,
    },
    {
      term: 'Antioxidants',
      category: 'ingredients',
      definition: 'Substances that protect the skin from free radical damage caused by environmental factors like UV radiation and pollution. Common examples include vitamin C, vitamin E, and niacinamide.',
      relatedTerms: ['Free Radicals', 'Vitamin C', 'Environmental Protection'],
      isPopular: true,
    },
    {
      term: 'Beta Hydroxy Acid (BHA)',
      category: 'ingredients',
      definition: 'Oil-soluble acid, primarily salicylic acid, that can penetrate into pores to remove buildup and reduce inflammation. Particularly effective for acne-prone and oily skin.',
      relatedTerms: ['Salicylic Acid', 'Pore Cleansing', 'Oil-Soluble'],
      isPopular: true,
    },
    {
      term: 'Ceramides',
      category: 'ingredients',
      definition: 'Lipid molecules that naturally occur in the skin barrier, helping to retain moisture and protect against environmental damage. Essential for maintaining healthy skin barrier function.',
      relatedTerms: ['Skin Barrier', 'Lipids', 'Moisture Retention'],
      isPopular: true,
    },
    {
      term: 'Chemical Exfoliation',
      category: 'treatments',
      definition: 'The process of removing dead skin cells using acids (AHAs, BHAs) rather than physical scrubs. Generally gentler and more effective than physical exfoliation.',
      relatedTerms: ['AHA', 'BHA', 'Cell Turnover'],
      isPopular: false,
    },
    {
      term: 'Comedogenic',
      category: 'terminology',
      definition: 'Refers to ingredients or products that have the potential to clog pores and cause comedones (blackheads and whiteheads). Non-comedogenic products are less likely to cause breakouts.',
      relatedTerms: ['Pore-Clogging', 'Acne-Prone', 'Non-Comedogenic'],
      isPopular: false,
    },
    {
      term: 'Dermatitis',
      category: 'conditions',
      definition: 'A general term for inflammation of the skin, characterized by redness, swelling, and irritation. Can be caused by allergies, irritants, or underlying skin conditions.',
      relatedTerms: ['Inflammation', 'Eczema', 'Contact Dermatitis'],
      isPopular: false,
    },
    {
      term: 'Emollient',
      category: 'ingredients',
      definition: 'Ingredients that soften and smooth the skin by filling in gaps between skin cells. Help improve skin texture and provide a protective barrier.',
      relatedTerms: ['Moisturizing', 'Skin Softening', 'Barrier Function'],
      isPopular: false,
    },
    {
      term: 'Free Radicals',
      category: 'terminology',
      definition: 'Unstable molecules that can damage skin cells and accelerate aging. Generated by UV exposure, pollution, and normal metabolic processes. Neutralized by antioxidants.',
      relatedTerms: ['Oxidative Stress', 'Antioxidants', 'Environmental Damage'],
      isPopular: false,
    },
    {
      term: 'Glycolic Acid',
      category: 'ingredients',
      definition: 'The smallest molecule AHA derived from sugar cane. Penetrates deeply to exfoliate skin, improve texture, and reduce signs of aging. Can be irritating in high concentrations.',
      relatedTerms: ['AHA', 'Chemical Exfoliation', 'Anti-Aging'],
      isPopular: true,
    },
    {
      term: 'Humectant',
      category: 'ingredients',
      definition: 'Ingredients that attract and bind moisture from the environment to the skin. Examples include hyaluronic acid, glycerin, and sodium PCA.',
      relatedTerms: ['Moisture Binding', 'Hydration', 'Water Attraction'],
      isPopular: false,
    },
    {
      term: 'Hyaluronic Acid',
      category: 'ingredients',
      definition: 'A powerful humectant that can hold up to 1,000 times its weight in water. Naturally occurs in skin and helps maintain hydration and plumpness.',
      relatedTerms: ['Hydration', 'Plumping', 'Moisture Retention'],
      isPopular: true,
    },
    {
      term: 'Hyperpigmentation',
      category: 'conditions',
      definition: 'Areas of skin that appear darker than surrounding skin due to excess melanin production. Can be caused by sun damage, acne, or hormonal changes.',
      relatedTerms: ['Melanin', 'Dark Spots', 'Post-Inflammatory Hyperpigmentation'],
      isPopular: true,
    },
    {
      term: 'Lactic Acid',
      category: 'ingredients',
      definition: 'A gentle AHA derived from fermented milk that exfoliates while providing hydrating benefits. Less irritating than glycolic acid, making it suitable for sensitive skin.',
      relatedTerms: ['AHA', 'Gentle Exfoliation', 'Hydrating'],
      isPopular: false,
    },
    {
      term: 'Melanin',
      category: 'terminology',
      definition: 'The pigment responsible for skin, hair, and eye color. Produced by melanocytes in response to UV exposure as a protective mechanism.',
      relatedTerms: ['Pigmentation', 'UV Protection', 'Skin Color'],
      isPopular: false,
    },
    {
      term: 'Niacinamide',
      category: 'ingredients',
      definition: 'A form of vitamin B3 that helps regulate oil production, reduce inflammation, minimize pore appearance, and improve skin texture. Well-tolerated by most skin types.',
      relatedTerms: ['Vitamin B3', 'Oil Control', 'Pore Minimizing'],
      isPopular: true,
    },
    {
      term: 'Occlusive',
      category: 'ingredients',
      definition: 'Ingredients that form a protective barrier on the skin surface to prevent water loss. Examples include petrolatum, mineral oil, and dimethicone.',
      relatedTerms: ['Barrier Protection', 'Water Loss Prevention', 'Sealing'],
      isPopular: false,
    },
    {
      term: 'Peptides',
      category: 'ingredients',
      definition: 'Short chains of amino acids that can signal skin cells to produce more collagen, improve skin texture, and reduce signs of aging.',
      relatedTerms: ['Collagen Production', 'Anti-Aging', 'Amino Acids'],
      isPopular: true,
    },
    {
      term: 'pH',
      category: 'terminology',
      definition: 'A measure of acidity or alkalinity on a scale of 0-14. Healthy skin has a slightly acidic pH of around 5.5, which helps maintain the skin barrier.',
      relatedTerms: ['Acid Mantle', 'Skin Barrier', 'Product Formulation'],
      isPopular: false,
    },
    {
      term: 'Purging',
      category: 'terminology',
      definition: 'A temporary worsening of skin condition when starting active ingredients that increase cell turnover, bringing underlying congestion to the surface.',
      relatedTerms: ['Cell Turnover', 'Breakouts', 'Adjustment Period'],
      isPopular: false,
    },
    {
      term: 'Retinoids',
      category: 'ingredients',
      definition: 'Vitamin A derivatives that increase cell turnover, stimulate collagen production, and improve skin texture. Include retinol, retinyl palmitate, and prescription tretinoin.',
      relatedTerms: ['Vitamin A', 'Cell Turnover', 'Anti-Aging'],
      isPopular: true,
    },
    {
      term: 'Rosacea',
      category: 'conditions',
      definition: 'A chronic inflammatory skin condition characterized by facial redness, visible blood vessels, and sometimes acne-like bumps. Triggered by various factors including sun, heat, and certain ingredients.',
      relatedTerms: ['Facial Redness', 'Inflammation', 'Sensitive Skin'],
      isPopular: false,
    },
    {
      term: 'Salicylic Acid',
      category: 'ingredients',
      definition: 'A BHA that penetrates into pores to remove buildup and reduce inflammation. Particularly effective for acne-prone skin and blackhead removal.',
      relatedTerms: ['BHA', 'Pore Cleansing', 'Acne Treatment'],
      isPopular: true,
    },
    {
      term: 'Sebum',
      category: 'terminology',
      definition: 'The natural oil produced by sebaceous glands to lubricate and protect the skin. Overproduction can lead to oily skin and acne.',
      relatedTerms: ['Natural Oil', 'Sebaceous Glands', 'Oily Skin'],
      isPopular: false,
    },
    {
      term: 'SPF (Sun Protection Factor)',
      category: 'terminology',
      definition: 'A measure of how well a sunscreen protects against UVB rays. SPF 30 blocks about 97% of UVB rays, while SPF 50 blocks about 98%.',
      relatedTerms: ['Sunscreen', 'UV Protection', 'UVB Rays'],
      isPopular: true,
    },
    {
      term: 'Vitamin C',
      category: 'ingredients',
      definition: 'A powerful antioxidant that brightens skin, stimulates collagen production, and protects against environmental damage. Most effective in L-ascorbic acid form.',
      relatedTerms: ['Antioxidant', 'Brightening', 'Collagen Stimulation'],
      isPopular: true,
    },
  ];

  const filteredTerms = glossaryTerms.filter(term => {
    const matchesSearch = term.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         term.definition.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || term.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const popularTerms = glossaryTerms.filter(term => term.isPopular);

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Complete Skincare Glossary - 100+ Terms Defined | Expert Dictionary"
        description="Master skincare terminology with our comprehensive glossary. From ingredients to conditions, get expert definitions for 100+ essential skincare terms and build your knowledge."
        keywords="skincare glossary, skincare dictionary, skincare terms, ingredient definitions, skin condition definitions, skincare terminology, beauty glossary"
        canonicalUrl="https://www.skincarecompass.com/glossary"
      />

      {/* Hero Section */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
              <BookOpen className="w-8 h-8 text-brand-teal" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-6">
              Skincare Glossary
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed mb-8">
              Master skincare terminology with our comprehensive dictionary. From complex ingredients 
              to skin conditions, get clear, expert definitions for 100+ essential terms.
            </p>
            
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-teal">{glossaryTerms.length}+</div>
                <div className="text-sm text-gray-600">Terms Defined</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-teal">4</div>
                <div className="text-sm text-gray-600">Categories</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-teal">{popularTerms.length}</div>
                <div className="text-sm text-gray-600">Popular Terms</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brand-teal">100%</div>
                <div className="text-sm text-gray-600">Expert Verified</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search terms and definitions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
              </div>
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="pl-10 pr-8 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent appearance-none bg-white"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Terms */}
      {searchTerm === '' && selectedCategory === 'all' && (
        <div className="section-padding bg-brand-off-white">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold text-brand-charcoal mb-8 flex items-center">
                <Star className="w-6 h-6 text-brand-teal mr-2" />
                Popular Terms
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                {popularTerms.slice(0, 6).map((term, index) => (
                  <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                    <h3 className="text-lg font-bold text-brand-charcoal mb-2">{term.term}</h3>
                    <p className="text-gray-600 text-sm mb-3">{term.definition}</p>
                    <div className="flex flex-wrap gap-2">
                      {term.relatedTerms.map((related, relatedIndex) => (
                        <span key={relatedIndex} className="px-2 py-1 bg-brand-teal/10 text-brand-teal text-xs rounded-full">
                          {related}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* All Terms */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-brand-charcoal mb-8">
              {searchTerm || selectedCategory !== 'all' ? 'Search Results' : 'All Terms'} 
              <span className="text-gray-500 font-normal ml-2">({filteredTerms.length} terms)</span>
            </h2>
            
            <div className="space-y-6">
              {filteredTerms.map((term, index) => (
                <div key={index} className="bg-brand-off-white rounded-lg p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-xl font-bold text-brand-charcoal">{term.term}</h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      term.category === 'ingredients' ? 'bg-blue-100 text-blue-700' :
                      term.category === 'conditions' ? 'bg-red-100 text-red-700' :
                      term.category === 'treatments' ? 'bg-green-100 text-green-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {categories.find(cat => cat.id === term.category)?.name}
                    </span>
                  </div>
                  <p className="text-gray-600 leading-relaxed mb-4">{term.definition}</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="text-sm text-gray-500 mr-2">Related:</span>
                    {term.relatedTerms.map((related, relatedIndex) => (
                      <span key={relatedIndex} className="px-2 py-1 bg-white text-brand-teal text-sm rounded border border-brand-teal/20">
                        {related}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {filteredTerms.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Search className="w-12 h-12 mx-auto" />
                </div>
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No terms found</h3>
                <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkincareGlossaryPage;
