import React, { useState } from 'react';
import { Search, Plus, X, CheckCircle, AlertTriangle, XCircle, Info, Zap } from 'lucide-react';

interface Ingredient {
  id: string;
  name: string;
  category: string;
  commonNames: string[];
}

interface CompatibilityResult {
  ingredient1: string;
  ingredient2: string;
  compatibility: 'excellent' | 'good' | 'caution' | 'avoid';
  explanation: string;
  recommendation: string;
  timeOfUse?: string;
  riskLevel: 'none' | 'low' | 'medium' | 'high';
}

const CompatibilityChecker: React.FC = () => {
  const [selectedIngredients, setSelectedIngredients] = useState<Ingredient[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showResults, setShowResults] = useState(false);

  // Sample ingredients database
  const ingredientsDatabase: Ingredient[] = [
    { id: '1', name: 'Vitamin C', category: 'Antioxidant', commonNames: ['L-Ascorbic Acid', 'Magnesium Ascorbyl Phosphate'] },
    { id: '2', name: 'Retinol', category: 'Anti-aging', commonNames: ['Vitamin A', 'Retinyl Palmitate'] },
    { id: '3', name: 'Niacinamide', category: 'Vitamin', commonNames: ['Vitamin B3', 'Nicotinamide'] },
    { id: '4', name: 'Hyaluronic Acid', category: 'Hydration', commonNames: ['Sodium Hyaluronate', 'HA'] },
    { id: '5', name: 'Salicylic Acid', category: 'Exfoliant', commonNames: ['BHA', 'Beta Hydroxy Acid'] },
    { id: '6', name: 'Glycolic Acid', category: 'Exfoliant', commonNames: ['AHA', 'Alpha Hydroxy Acid'] },
    { id: '7', name: 'Benzoyl Peroxide', category: 'Acne Treatment', commonNames: ['BP'] },
    { id: '8', name: 'Peptides', category: 'Anti-aging', commonNames: ['Copper Peptides', 'Matrixyl'] },
  ];

  // Compatibility rules database
  const compatibilityRules: CompatibilityResult[] = [
    {
      ingredient1: 'Vitamin C',
      ingredient2: 'Niacinamide',
      compatibility: 'good',
      explanation: 'Modern formulations are stable when combined, despite old myths about incompatibility.',
      recommendation: 'Can be used together, but some may experience temporary flushing.',
      timeOfUse: 'Morning or evening',
      riskLevel: 'low'
    },
    {
      ingredient1: 'Vitamin C',
      ingredient2: 'Retinol',
      compatibility: 'caution',
      explanation: 'Both are potent actives that can increase skin sensitivity when combined.',
      recommendation: 'Use Vitamin C in morning, Retinol at night for best results.',
      timeOfUse: 'Separate times',
      riskLevel: 'medium'
    },
    {
      ingredient1: 'Retinol',
      ingredient2: 'Benzoyl Peroxide',
      compatibility: 'avoid',
      explanation: 'BP can oxidize retinol, reducing effectiveness and increasing irritation.',
      recommendation: 'Use at different times or alternate days completely.',
      timeOfUse: 'Never together',
      riskLevel: 'high'
    },
    {
      ingredient1: 'Niacinamide',
      ingredient2: 'Hyaluronic Acid',
      compatibility: 'excellent',
      explanation: 'Perfect combination that enhances each other\'s benefits.',
      recommendation: 'Apply HA first, then niacinamide for optimal absorption.',
      timeOfUse: 'Morning and evening',
      riskLevel: 'none'
    },
    {
      ingredient1: 'Salicylic Acid',
      ingredient2: 'Retinol',
      compatibility: 'caution',
      explanation: 'Both can cause dryness and irritation when used together.',
      recommendation: 'Alternate nights or use SA in morning, retinol at night.',
      timeOfUse: 'Separate times',
      riskLevel: 'medium'
    }
  ];

  const filteredIngredients = ingredientsDatabase.filter(ingredient =>
    ingredient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ingredient.commonNames.some(name => name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const addIngredient = (ingredient: Ingredient) => {
    if (!selectedIngredients.find(item => item.id === ingredient.id)) {
      setSelectedIngredients([...selectedIngredients, ingredient]);
      setSearchTerm('');
    }
  };

  const removeIngredient = (ingredientId: string) => {
    setSelectedIngredients(selectedIngredients.filter(item => item.id !== ingredientId));
  };

  const checkCompatibility = () => {
    setShowResults(true);
  };

  const getCompatibilityResults = (): CompatibilityResult[] => {
    const results: CompatibilityResult[] = [];
    
    for (let i = 0; i < selectedIngredients.length; i++) {
      for (let j = i + 1; j < selectedIngredients.length; j++) {
        const ingredient1 = selectedIngredients[i].name;
        const ingredient2 = selectedIngredients[j].name;
        
        // Find matching rule (check both directions)
        let rule = compatibilityRules.find(r => 
          (r.ingredient1 === ingredient1 && r.ingredient2 === ingredient2) ||
          (r.ingredient1 === ingredient2 && r.ingredient2 === ingredient1)
        );
        
        // If no specific rule found, assume good compatibility for most combinations
        if (!rule) {
          rule = {
            ingredient1,
            ingredient2,
            compatibility: 'good',
            explanation: 'No known negative interactions between these ingredients.',
            recommendation: 'Can generally be used together safely.',
            timeOfUse: 'Morning or evening',
            riskLevel: 'none'
          };
        }
        
        results.push(rule);
      }
    }
    
    return results;
  };

  const getCompatibilityIcon = (compatibility: string) => {
    switch (compatibility) {
      case 'excellent':
        return <CheckCircle className="w-6 h-6 text-green-600" />;
      case 'good':
        return <CheckCircle className="w-6 h-6 text-blue-600" />;
      case 'caution':
        return <AlertTriangle className="w-6 h-6 text-yellow-600" />;
      case 'avoid':
        return <XCircle className="w-6 h-6 text-red-600" />;
      default:
        return <Info className="w-6 h-6 text-gray-600" />;
    }
  };

  const getCompatibilityColor = (compatibility: string) => {
    switch (compatibility) {
      case 'excellent':
        return 'bg-green-50 border-green-200';
      case 'good':
        return 'bg-blue-50 border-blue-200';
      case 'caution':
        return 'bg-yellow-50 border-yellow-200';
      case 'avoid':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="bg-white rounded-2xl p-6 shadow-sm">
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-12 h-12 bg-brand-teal/10 rounded-full flex items-center justify-center">
          <Zap className="w-6 h-6 text-brand-teal" />
        </div>
        <div>
          <h3 className="text-2xl font-bold text-brand-charcoal">Ingredient Compatibility Checker</h3>
          <p className="text-gray-600">Check if your skincare ingredients work well together</p>
        </div>
      </div>

      {/* Ingredient Search */}
      <div className="mb-6">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search for ingredients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
          />
        </div>

        {/* Search Results */}
        {searchTerm && (
          <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
            {filteredIngredients.map((ingredient) => (
              <button
                key={ingredient.id}
                onClick={() => addIngredient(ingredient)}
                className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:outline-none focus:bg-brand-teal/5"
              >
                <div className="font-medium text-brand-charcoal">{ingredient.name}</div>
                <div className="text-sm text-gray-600">{ingredient.category}</div>
                {ingredient.commonNames.length > 0 && (
                  <div className="text-xs text-gray-500 mt-1">
                    Also known as: {ingredient.commonNames.join(', ')}
                  </div>
                )}
              </button>
            ))}
            {filteredIngredients.length === 0 && (
              <div className="px-4 py-3 text-gray-500 text-center">
                No ingredients found
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selected Ingredients */}
      <div className="mb-6">
        <h4 className="font-semibold text-brand-charcoal mb-3">Selected Ingredients ({selectedIngredients.length})</h4>
        {selectedIngredients.length > 0 ? (
          <div className="flex flex-wrap gap-2 mb-4">
            {selectedIngredients.map((ingredient) => (
              <div
                key={ingredient.id}
                className="flex items-center space-x-2 bg-brand-teal/10 text-brand-teal px-3 py-2 rounded-full"
              >
                <span className="text-sm font-medium">{ingredient.name}</span>
                <button
                  onClick={() => removeIngredient(ingredient.id)}
                  className="hover:bg-brand-teal/20 rounded-full p-1"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
            Add ingredients to check their compatibility
          </p>
        )}

        {selectedIngredients.length >= 2 && (
          <button
            onClick={checkCompatibility}
            className="btn-primary w-full"
          >
            Check Compatibility
          </button>
        )}
      </div>

      {/* Results */}
      {showResults && selectedIngredients.length >= 2 && (
        <div className="space-y-4">
          <h4 className="font-semibold text-brand-charcoal mb-4">Compatibility Results</h4>
          {getCompatibilityResults().map((result, index) => (
            <div
              key={index}
              className={`rounded-lg border p-4 ${getCompatibilityColor(result.compatibility)}`}
            >
              <div className="flex items-start space-x-3">
                {getCompatibilityIcon(result.compatibility)}
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h5 className="font-semibold text-brand-charcoal">
                      {result.ingredient1} + {result.ingredient2}
                    </h5>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      result.compatibility === 'excellent' ? 'bg-green-100 text-green-800' :
                      result.compatibility === 'good' ? 'bg-blue-100 text-blue-800' :
                      result.compatibility === 'caution' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {result.compatibility.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-gray-700 mb-2">{result.explanation}</p>
                  <div className="bg-white/50 rounded p-3">
                    <p className="font-medium text-brand-charcoal mb-1">Recommendation:</p>
                    <p className="text-gray-700">{result.recommendation}</p>
                    {result.timeOfUse && (
                      <p className="text-sm text-gray-600 mt-1">
                        <strong>Best time to use:</strong> {result.timeOfUse}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Overall Assessment */}
          <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-lg p-4 mt-6">
            <h5 className="font-semibold text-brand-charcoal mb-2">Overall Assessment</h5>
            <p className="text-gray-700">
              Based on the compatibility analysis, your selected ingredients can generally be used together 
              with proper timing and application techniques. Always patch test new combinations and introduce 
              products gradually to monitor your skin's response.
            </p>
          </div>
        </div>
      )}

      {/* Disclaimer */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">
          <strong>Disclaimer:</strong> This tool provides general guidance based on known ingredient interactions. 
          Individual skin reactions may vary. Always patch test new products and consult with a dermatologist 
          for personalized advice, especially if you have sensitive skin or specific skin conditions.
        </p>
      </div>
    </div>
  );
};

export default CompatibilityChecker;
