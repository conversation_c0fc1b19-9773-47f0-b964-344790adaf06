import React from 'react';
import { Link } from 'react-router-dom';
import { CheckCircle, Users, BarChart3, ArrowRight, Star, Microscope, Calendar } from 'lucide-react';

const FeaturedTools: React.FC = () => {
  const tools = [
    {
      slug: 'ingredient-compatibility-checker',
      title: 'Ingredient Compatibility Checker',
      description: 'Check if your skincare ingredients work well together or if there are potential conflicts in your routine.',
      icon: CheckCircle,
      color: 'bg-green-500',
      features: ['Real-time analysis', 'Detailed explanations', 'Safety recommendations'],
      users: '25K+',
      rating: 4.8,
      isPopular: true,
    },
    {
      slug: 'routine-builder-quiz',
      title: 'Personalized Routine Builder',
      description: 'Take our comprehensive quiz to get a customized skincare routine based on your skin type and concerns.',
      icon: Users,
      color: 'bg-blue-500',
      features: ['Personalized recommendations', 'Product suggestions', 'Step-by-step routine'],
      users: '18K+',
      rating: 4.7,
      isPopular: true,
    },
    {
      slug: 'ingredient-analyzer',
      title: 'Ingredient Analyzer',
      description: 'Analyze any skincare ingredient for safety, effectiveness, and compatibility with your skin type.',
      icon: Microscope,
      color: 'bg-purple-500',
      features: ['Safety analysis', 'Effectiveness ratings', 'Skin compatibility'],
      users: '12K+',
      rating: 4.6,
      isPopular: true,
    },
    {
      slug: 'routine-tracker',
      title: 'Routine Tracker',
      description: 'Track your daily skincare routine, monitor progress, and get personalized insights.',
      icon: Calendar,
      color: 'bg-indigo-500',
      features: ['Progress tracking', 'Photo timeline', 'Personalized insights'],
      users: '15K+',
      rating: 4.7,
      isPopular: true,
    },
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Featured Tools
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Powerful, science-backed tools to help you make informed decisions 
            about your skincare routine and ingredient combinations.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {tools.map((tool, index) => (
            <div
              key={tool.slug}
              className="card p-8 hover:scale-105 transition-all duration-300 group animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-start justify-between mb-6">
                <div className={`inline-flex items-center justify-center w-16 h-16 ${tool.color} rounded-xl group-hover:scale-110 transition-transform duration-200`}>
                  <tool.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-right">
                  {tool.isPopular && (
                    <span className="text-xs bg-orange-100 text-orange-800 px-3 py-1 rounded-full mb-2 block">
                      Most Popular
                    </span>
                  )}
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{tool.rating}</span>
                  </div>
                </div>
              </div>

              <h3 className="text-2xl font-bold text-brand-charcoal mb-4 group-hover:text-brand-teal transition-colors duration-200">
                {tool.title}
              </h3>

              <p className="text-gray-600 mb-6 leading-relaxed">
                {tool.description}
              </p>

              <div className="space-y-3 mb-6">
                <div className="text-sm font-medium text-gray-700">Key Features:</div>
                <ul className="space-y-2">
                  {tool.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-brand-teal rounded-full"></div>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex items-center justify-between pt-6 border-t border-gray-100">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <BarChart3 className="w-4 h-4" />
                    <span>{tool.users} users</span>
                  </div>
                </div>
                <Link
                  to={`/tools/${tool.slug}`}
                  className="inline-flex items-center space-x-2 btn-primary"
                >
                  <span>Try It Free</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
          <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
            More Tools Coming Soon
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            We're constantly developing new tools to help you optimize your skincare routine. 
            Join our community to be the first to know about new releases.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/tools" className="btn-primary">
              View All Tools
            </Link>
            <button className="btn-secondary">
              Join Community
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedTools;