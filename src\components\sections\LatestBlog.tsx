import React from 'react';
import { Link } from 'react-router-dom';
import { Clock, TrendingUp, ArrowRight } from 'lucide-react';

const LatestBlog: React.FC = () => {
  const latestPosts = [
    {
      slug: 'retinol-vs-bakuchiol-complete-comparison',
      title: 'Retinol vs. Bakuchiol: The Complete Scientific Comparison',
      excerpt: 'Dive deep into the science behind these two popular anti-aging ingredients and discover which one is right for your skin.',
      readTime: '12 min read',
      publishDate: 'June 15, 2025',
      category: 'Science Explained',
      image: 'https://images.pexels.com/photos/3621234/pexels-photo-3621234.jpeg?auto=compress&cs=tinysrgb&w=400',
      isTrending: true,
    },
    {
      slug: 'niacinamide-complete-guide',
      title: 'Niacinamide: The Complete Guide to Vitamin B3 in Skincare',
      excerpt: 'Everything you need to know about niacinamide, from its benefits to how to incorporate it into your routine safely.',
      readTime: '8 min read',
      publishDate: 'June 12, 2025',
      category: 'Ingredient Spotlight',
      image: 'https://images.pexels.com/photos/7290720/pexels-photo-7290720.jpeg?auto=compress&cs=tinysrgb&w=400',
      isTrending: false,
    },
    {
      slug: 'morning-routine-sensitive-skin',
      title: 'Building a Morning Routine for Sensitive Skin',
      excerpt: 'A gentle approach to morning skincare that protects and nourishes sensitive skin without causing irritation.',
      readTime: '6 min read',
      publishDate: 'June 10, 2025',
      category: 'Routine Guides',
      image: 'https://images.pexels.com/photos/7290714/pexels-photo-7290714.jpeg?auto=compress&cs=tinysrgb&w=400',
      isTrending: true,
    },
  ];

  return (
    <section className="section-padding bg-brand-off-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Latest from the Blog
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Stay updated with the latest skincare science, ingredient discoveries, 
            and expert insights from our team of dermatologists and researchers.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {latestPosts.map((post, index) => (
            <Link
              key={post.slug}
              to={`/blog/${post.slug}`}
              className="card overflow-hidden hover:scale-105 transition-all duration-300 group animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="relative">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="text-xs bg-white/90 backdrop-blur-sm text-gray-700 px-3 py-1 rounded-full font-medium">
                    {post.category}
                  </span>
                </div>
                {post.isTrending && (
                  <div className="absolute top-4 right-4">
                    <div className="bg-orange-500 text-white p-1.5 rounded-full">
                      <TrendingUp className="w-3 h-3" />
                    </div>
                  </div>
                )}
              </div>

              <div className="p-6">
                <h3 className="text-lg font-bold text-brand-charcoal mb-3 group-hover:text-brand-teal transition-colors duration-200 line-clamp-2">
                  {post.title}
                </h3>

                <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3">
                  {post.excerpt}
                </p>

                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {post.publishDate}
                  </span>
                  <ArrowRight className="w-4 h-4 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center">
          <Link to="/blog" className="btn-primary">
            Read More Articles
          </Link>
        </div>
      </div>
    </section>
  );
};

export default LatestBlog;