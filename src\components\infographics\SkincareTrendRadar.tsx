import React from 'react';
import { <PERSON>rendingUp, Zap, Star, Sparkles, Target, ArrowUp, ArrowDown, Minus } from 'lucide-react';

const SkincareTrendRadar: React.FC = () => {
  const trendingIngredients = [
    {
      name: 'Exosome Serum',
      growth: '+557%',
      popularity: 78,
      innovation: 95,
      category: 'Next-Gen',
      color: 'from-purple-500 to-indigo-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      trend: 'up',
      marketValue: '$400M by 2025',
      description: 'Cellular regeneration technology',
    },
    {
      name: '<PERSON>f Tallow',
      growth: '+472%',
      popularity: 72,
      innovation: 65,
      category: 'Traditional',
      color: 'from-amber-500 to-orange-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      trend: 'up',
      marketValue: 'Viral on TikTok',
      description: 'Return to ancestral skincare',
    },
    {
      name: 'Glass Skin Mask',
      growth: '+253%',
      popularity: 85,
      innovation: 75,
      category: 'K-Beauty',
      color: 'from-blue-500 to-cyan-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      trend: 'up',
      marketValue: '2.1B views',
      description: 'Korean beauty standard',
    },
    {
      name: 'Tranexamic Acid',
      growth: '+107%',
      popularity: 73,
      innovation: 80,
      category: 'Clinical',
      color: 'from-green-500 to-emerald-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      trend: 'up',
      marketValue: 'Dermatologist favorite',
      description: 'Medical-grade brightening',
    },
    {
      name: 'Bakuchiol',
      growth: '+89%',
      popularity: 74,
      innovation: 82,
      category: 'Plant-Based',
      color: 'from-rose-500 to-pink-600',
      bgColor: 'bg-rose-50',
      borderColor: 'border-rose-200',
      trend: 'stable',
      marketValue: 'Pregnancy-safe',
      description: 'Natural retinol alternative',
    },
    {
      name: 'Peptide Complex',
      growth: '+45%',
      popularity: 68,
      innovation: 78,
      category: 'Anti-Aging',
      color: 'from-violet-500 to-purple-600',
      bgColor: 'bg-violet-50',
      borderColor: 'border-violet-200',
      trend: 'stable',
      marketValue: 'Proven efficacy',
      description: 'Collagen stimulation',
    },
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <ArrowDown className="w-4 h-4 text-red-600" />;
      default:
        return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  const marketInsights = [
    {
      title: 'Next-Gen Technology',
      value: '$2.3B',
      change: '+127%',
      description: 'Exosome and cellular therapy market',
      icon: Sparkles,
      color: 'text-purple-600',
    },
    {
      title: 'Traditional Revival',
      value: '45M',
      change: '+89%',
      description: 'Social media mentions of ancestral skincare',
      icon: Target,
      color: 'text-amber-600',
    },
    {
      title: 'K-Beauty Influence',
      value: '78%',
      change: '+23%',
      description: 'Global market share of Korean trends',
      icon: Star,
      color: 'text-blue-600',
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-brand-teal to-brand-teal-light rounded-full mb-4">
          <TrendingUp className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-brand-charcoal mb-2">
          2025 Skincare Trend Radar
        </h2>
        <p className="text-gray-600">
          Real-time tracking of the fastest growing ingredients and technologies
        </p>
      </div>

      {/* Market Insights */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        {marketInsights.map((insight, index) => (
          <div key={index} className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border border-gray-100">
            <div className="flex items-center justify-between mb-3">
              <insight.icon className={`w-6 h-6 ${insight.color}`} />
              <span className="text-sm font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">
                {insight.change}
              </span>
            </div>
            <div className="text-2xl font-bold text-brand-charcoal mb-1">{insight.value}</div>
            <div className="text-sm font-medium text-gray-700 mb-1">{insight.title}</div>
            <div className="text-xs text-gray-500">{insight.description}</div>
          </div>
        ))}
      </div>

      {/* Trending Ingredients Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {trendingIngredients.map((ingredient, index) => (
          <div
            key={index}
            className={`${ingredient.bgColor} rounded-xl p-6 border ${ingredient.borderColor} hover:shadow-lg transition-all duration-300 animate-slide-up`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`w-10 h-10 rounded-full bg-gradient-to-r ${ingredient.color} flex items-center justify-center text-white font-bold text-sm`}>
                {index + 1}
              </div>
              <div className="flex items-center space-x-2">
                {getTrendIcon(ingredient.trend)}
                <span className="text-sm font-bold text-red-600 bg-red-100 px-2 py-1 rounded-full">
                  {ingredient.growth}
                </span>
              </div>
            </div>

            <h3 className="font-bold text-brand-charcoal mb-2">{ingredient.name}</h3>
            <p className="text-sm text-gray-600 mb-3">{ingredient.description}</p>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Popularity</span>
                <span className="font-medium">{ingredient.popularity}%</span>
              </div>
              <div className="w-full bg-white rounded-full h-2">
                <div 
                  className={`bg-gradient-to-r ${ingredient.color} h-2 rounded-full transition-all duration-1000`}
                  style={{ width: `${ingredient.popularity}%` }}
                ></div>
              </div>

              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-500">Innovation Score</span>
                <span className="font-medium">{ingredient.innovation}%</span>
              </div>
              <div className="w-full bg-white rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full transition-all duration-1000"
                  style={{ width: `${ingredient.innovation}%` }}
                ></div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-white/50">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-600">{ingredient.category}</span>
                <span className="text-xs text-gray-500">{ingredient.marketValue}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Trend Analysis */}
      <div className="bg-gradient-to-r from-gray-50 to-white rounded-xl p-6 border border-gray-100">
        <div className="flex items-center space-x-2 mb-4">
          <Zap className="w-5 h-5 text-brand-teal" />
          <h3 className="font-bold text-brand-charcoal">2025 Trend Analysis</h3>
        </div>
        <div className="grid md:grid-cols-2 gap-6 text-sm">
          <div>
            <h4 className="font-semibold text-brand-charcoal mb-2">🚀 Explosive Growth</h4>
            <ul className="text-gray-600 space-y-1">
              <li>• Next-gen cellular technologies leading innovation</li>
              <li>• Traditional ingredients making viral comebacks</li>
              <li>• Medical-grade actives becoming mainstream</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-brand-charcoal mb-2">🎯 Key Drivers</h4>
            <ul className="text-gray-600 space-y-1">
              <li>• Social media influence on ingredient discovery</li>
              <li>• Demand for pregnancy-safe alternatives</li>
              <li>• K-beauty's continued global expansion</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkincareTrendRadar;