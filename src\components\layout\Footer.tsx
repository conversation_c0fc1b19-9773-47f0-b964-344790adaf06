import React from 'react';
import { Link } from 'react-router-dom';
import { Beaker, Mail } from 'lucide-react';

const Footer: React.FC = () => {
  const footerSections = [
    {
      title: 'Ingredients',
      links: [
        { label: 'Browse All Ingredients', href: '/ingredients' },
        { label: 'Popular Ingredients', href: '/ingredients?filter=popular' },
        { label: 'New Additions', href: '/ingredients?filter=new' },
        { label: 'Ingredient Dictionary', href: '/beginners/ingredient-dictionary' },
      ],
    },
    {
      title: 'Combinations',
      links: [
        { label: 'All Combinations', href: '/combinations' },
        { label: 'Popular Combinations', href: '/combinations?filter=popular' },
        { label: 'Compatibility Checker', href: '/tools/ingredient-compatibility-checker' },
        { label: 'Routine Builder', href: '/tools/routine-builder-quiz' },
      ],
    },
    {
      title: 'Education',
      links: [
        { label: "Beginner's Hub", href: '/beginners' },
        { label: 'Skincare 101', href: '/beginners/skincare-101' },
        { label: 'How to Read Labels', href: '/beginners/how-to-read-labels' },
        { label: 'Blog', href: '/blog' },
      ],
    },
    {
      title: 'Company',
      links: [
        { label: 'About Us', href: '/about' },
        { label: 'FAQ', href: '/faq' },
        { label: 'Tools', href: '/tools' },
        { label: 'Privacy Policy', href: '/privacy' },
      ],
    },
  ];



  return (
    <footer className="bg-brand-charcoal text-white">
      <div className="container-custom">
        {/* Newsletter Section */}
        <div className="py-16 border-b border-gray-700">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold mb-4">
                Stay Updated with Skincare Science
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Get the latest ingredient discoveries, combination guides, and educational content 
                delivered to your inbox weekly.
              </p>
            </div>
            <div className="flex gap-4">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent text-white placeholder-gray-400"
              />
              <button className="btn-primary whitespace-nowrap">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid lg:grid-cols-5 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <Link to="/" className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-brand-teal rounded-lg flex items-center justify-center">
                  <Beaker className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">Skincare Compass</span>
              </Link>
              <p className="text-gray-300 leading-relaxed">
                The world's most trusted resource for skincare ingredient information and education.
              </p>
            </div>

            {/* Footer Links */}
            {footerSections.map((section, index) => (
              <div key={index}>
                <h4 className="font-semibold text-lg mb-4">{section.title}</h4>
                <ul className="space-y-3">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <Link
                        to={link.href}
                        className="text-gray-300 hover:text-brand-teal transition-colors duration-200"
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="py-8 border-t border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <p className="text-gray-300">
                © 2025 Skincare Compass. All rights reserved.
              </p>
              <div className="flex space-x-6 text-sm">
                <Link to="/privacy" className="text-gray-400 hover:text-brand-teal transition-colors duration-200">
                  Privacy Policy
                </Link>
                <Link to="/terms" className="text-gray-400 hover:text-brand-teal transition-colors duration-200">
                  Terms of Service
                </Link>
                <a href="#" className="text-gray-400 hover:text-brand-teal transition-colors duration-200">
                  Cookie Policy
                </a>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 text-gray-400">
              <Mail className="w-4 h-4" />
              <span className="text-sm"><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;