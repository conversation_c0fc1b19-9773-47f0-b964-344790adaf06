import React from 'react';
import { Bar<PERSON>hart3, Clock, TrendingUp, Download } from 'lucide-react';
import IngredientEfficacyChart from '../components/infographics/IngredientEfficacyChart';
import AMPMRoutineFlowchart from '../components/infographics/AMPMRoutineFlowchart';
import MetaTags from '../components/SEO/MetaTags';

const InfographicsPage: React.FC = () => {
  const infographics = [
    {
      id: 'ingredient-efficacy-chart',
      title: 'Ingredient Efficacy Comparison Chart',
      description: 'Compare the effectiveness of different ingredients for hyperpigmentation based on clinical studies.',
      component: IngredientEfficacyChart,
      category: 'Research',
      views: '15.2K',
      downloads: '3.4K',
    },
    {
      id: 'am-pm-routine-flowchart',
      title: 'AM vs. PM Routine Flowchart',
      description: 'Visual decision tree showing which ingredients are best for morning and night routines.',
      component: AMPMRoutineFlowchart,
      category: 'Routines',
      views: '22.8K',
      downloads: '5.1K',
    },
  ];

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title="Skincare Infographics & Visual Learning Tools"
        description="Interactive skincare charts, infographics, and visual guides. Learn skincare science through easy-to-understand visual tools and downloadable resources."
        keywords="skincare infographics, skincare charts, visual skincare guide, skincare education tools, interactive skincare learning"
        canonicalUrl="https://www.skincarecompass.com/infographics"
      />
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-12">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-6">
              <BarChart3 className="w-8 h-8 text-brand-teal" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Visual Learning Tools
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Complex skincare science made simple through interactive charts, 
              infographics, and visual guides.
            </p>
          </div>
        </div>
      </div>

      {/* Infographics */}
      <div className="section-padding">
        <div className="container-custom">
          <div className="space-y-16">
            {infographics.map((infographic, index) => {
              const Component = infographic.component;
              return (
                <div key={infographic.id} className="animate-slide-up" style={{ animationDelay: `${index * 0.2}s` }}>
                  <div className="text-center mb-8">
                    <div className="flex items-center justify-center space-x-4 mb-4">
                      <span className="text-sm bg-brand-teal text-white px-3 py-1 rounded-full">
                        {infographic.category}
                      </span>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="w-4 h-4" />
                          <span>{infographic.views} views</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Download className="w-4 h-4" />
                          <span>{infographic.downloads} downloads</span>
                        </div>
                      </div>
                    </div>
                    <h2 className="text-3xl font-bold text-brand-charcoal mb-4">
                      {infographic.title}
                    </h2>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      {infographic.description}
                    </p>
                  </div>
                  
                  <Component />
                  
                  <div className="text-center mt-6">
                    <button className="btn-secondary flex items-center space-x-2 mx-auto">
                      <Download className="w-4 h-4" />
                      <span>Download High-Res Version</span>
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8">
              More Visual Tools Coming Soon
            </h2>
            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="card p-6 opacity-75">
                <Clock className="w-8 h-8 text-gray-400 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-600 mb-2">pH Compatibility Matrix</h3>
                <p className="text-gray-500 text-sm">Visual guide to ingredient pH levels and compatibility</p>
              </div>
              <div className="card p-6 opacity-75">
                <Clock className="w-8 h-8 text-gray-400 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-600 mb-2">Skin Type Decision Tree</h3>
                <p className="text-gray-500 text-sm">Interactive flowchart to identify your skin type</p>
              </div>
              <div className="card p-6 opacity-75">
                <Clock className="w-8 h-8 text-gray-400 mx-auto mb-4" />
                <h3 className="font-semibold text-gray-600 mb-2">Ingredient Timeline</h3>
                <p className="text-gray-500 text-sm">When to expect results from different ingredients</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InfographicsPage;